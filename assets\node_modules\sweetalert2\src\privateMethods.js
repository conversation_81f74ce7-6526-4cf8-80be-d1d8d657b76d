/**
 * This module contains `WeakMap`s for each effectively-"private  property" that a `<PERSON>wal` has.
 * For example, to set the private property "foo" of `this` to "bar", you can `privateProps.foo.set(this, 'bar')`
 * This is the approach that Babel will probably take to implement private methods/fields
 *   https://github.com/tc39/proposal-private-methods
 *   https://github.com/babel/babel/pull/7555
 * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*
 *   then we can use that language feature.
 */

export default {
  swalPromiseResolve: new WeakMap(),
  swalPromiseReject: new WeakMap(),
}

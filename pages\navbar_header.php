<div class="header">
    <button id="toggle-sidebar"><i class="fas fa-bars"></i></button>

    <div class="container" id="header-logo-container">
        <div class="row align-items-center">
            <div class="col-auto">
                <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="img-fluid header-logo-icon" id="header-logo">
            </div>
            <div class="col">
                <div class="header-info">
                    <h5 class="mb-1">DICT Surigao del Norte Provincial Office</h5>
                    <p style="font-size: 14px;" class="header-address mb-0">Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p>
                </div>
            </div>
        </div>
    </div>


    <div class="d-flex justify-content-between align-items-center">
        <div style="display: flex; flex-direction: column; align-items: center; margin-left: 5px; text-transform: uppercase;">

            <div class="user-name-container">
                <span class="user-name"><?php echo isset($_SESSION['user_name']) ? htmlspecialchars($_SESSION['user_name']) : 'Guest'; ?></span>
            </div>

            <span style="font-size: 12px;"><?php echo isset($_SESSION['user_role']) ? htmlspecialchars($_SESSION['user_role']) : 'Guest'; ?></span>
        </div>

        <div style="height: 50px; width: 50px; display: flex; justify-content: center; align-items: center; cursor: pointer; border-radius: 50%; position: relative;">
            <?php if (isset($_SESSION['profile_image'])): ?>
                <img src="data:image/jpeg;base64,<?php echo $_SESSION['profile_image']; ?>" class="dropdown-toggle h-75 w-75 rounded-circle" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false" alt="User Profile">
            <?php else: ?>
                <img src="../assets/img/boy.png" class="dropdown-toggle h-75 w-75 rounded-circle" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false" alt="User Profile">
            <?php endif; ?>
            <ul class="dropdown-menu" style="position: absolute; top: 100%; right: 0; min-width: 180px;">

                <li><a class="dropdown-item py-2" href="profile-page.php">Profile Page<i class="fa fa-user ms-5"></i></a></li>
                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'Admin'): ?>
                    <li>
                        <a class="dropdown-item py-2" href="#" data-mdb-modal-init data-mdb-target="#switchAccountModalTop">
                            Switch Account<i class="fa fa-key ms-4"></i>
                        </a>
                    </li>
                <?php endif; ?>

            </ul>
        </div>
    </div>
</div>

<?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'Admin'): ?>
    <div class="modal top fade" id="switchAccountModalTop" tabindex="-1" aria-labelledby="switchAccountModalTopLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="switchAccountModalTopLabel">Switch Account</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="switchAccountFormTop">
                        <div data-mdb-input-init class="form-outline mb-4">
                            <input type="email" id="switchEmailTop" name="switchEmail" class="form-control" autocomplete="username" required />
                            <label class="form-label" for="switchEmailTop">Email address</label>
                        </div>

                        <div data-mdb-input-init class="form-outline ">
                            <input type="password" id="switchPasswordTop" name="switchPassword" class="form-control" autocomplete="current-password" required />
                            <label class="form-label" for="switchPasswordTop">Password</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer d-flex gap-2">
                    <button type="button" class="btn btn-secondary flex-grow-1" data-mdb-dismiss="modal">
                        Cancel
                    </button>
                    <button type="submit" form="switchAccountFormTop" class="btn btn-primary flex-grow-1">
                        Switch
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the switch account form
        const switchAccountForm = document.getElementById('switchAccountFormTop');

        if (switchAccountForm) {
            switchAccountForm.addEventListener('submit', function(event) {
                event.preventDefault();

                // Get form data
                const email = document.getElementById('switchEmailTop').value;
                const password = document.getElementById('switchPasswordTop').value;

                // Validate form data
                if (!email || !password) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Please fill in all fields',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                // Create form data object
                const formData = new FormData();
                formData.append('email', email);
                formData.append('password', password);

                // Send AJAX request
                fetch('switch_account.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            Swal.fire({
                                title: 'Success',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                // Redirect to the appropriate page
                                window.location.href = data.redirect;
                            });
                        } else {
                            // Show error message
                            Swal.fire({
                                title: 'Error',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            title: 'Error',
                            text: 'An error occurred. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    });
            });
        }
    });
</script>
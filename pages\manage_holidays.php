<?php
// manage_holidays.php

try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Handle get holiday data for editing
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['get_holiday'])) {
    try {
        $id = $_GET['id'];

        // Get holiday data
        $stmt = $connect->prepare("SELECT * FROM tbl_holidays WHERE id = ?");
        $stmt->execute([$id]);
        $holiday = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($holiday) {
            // Format date for display in form
            $holiday['holiday_date'] = date('Y-m-d', strtotime($holiday['holiday_date']));

            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $holiday
            ]);
        } else {
            // Return error JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Holiday not found'
            ]);
        }
        exit;
    } catch (Exception $e) {
        // Return error JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error getting holiday data: ' . $e->getMessage()
        ]);
        exit;
    }
}

// Handle update holiday request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_holiday'])) {
    try {
        // Get form data
        $id = $_POST['id'];
        $holidayName = $_POST['holiday_name'];
        $holidayDate = $_POST['holiday_date'];
        $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;

        // Validate inputs
        if (empty($holidayName) || empty($holidayDate)) {
            throw new Exception("Holiday name and date are required fields.");
        }

        // Check if holiday date already exists for another holiday
        $checkStmt = $connect->prepare("SELECT COUNT(*) FROM tbl_holidays WHERE holiday_date = ? AND id != ?");
        $checkStmt->execute([$holidayDate, $id]);
        $exists = $checkStmt->fetchColumn();

        if ($exists > 0) {
            throw new Exception("Another holiday already exists on this date.");
        }

        // Update database
        $stmt = $connect->prepare("UPDATE tbl_holidays SET holiday_name = ?, holiday_date = ?, is_recurring = ? WHERE id = ?");
        $stmt->execute([$holidayName, $holidayDate, $isRecurring, $id]);

        // Success message will be handled by SweetAlert
        $successMessage = "Holiday updated successfully!";

        // Return JSON response for AJAX requests
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => $successMessage]);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_holidays.php");
        exit;
    } catch (Exception $e) {
        // Error message will be handled by SweetAlert
        $errorMessage = "Error updating holiday: " . $e->getMessage();

        // Return JSON response for AJAX requests
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_holidays.php");
        exit;
    }
}

// Handle delete holiday request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_holiday'])) {
    try {
        $id = $_POST['id'];

        // Delete from database
        $stmt = $connect->prepare("DELETE FROM tbl_holidays WHERE id = ?");
        $stmt->execute([$id]);

        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Holiday deleted successfully!'
        ]);
        exit;
    } catch (Exception $e) {
        // Return error JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error deleting holiday: ' . $e->getMessage()
        ]);
        exit;
    }
}

// Handle form submission for adding a new holiday
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_holiday'])) {
    try {
        // Get form data
        $holidayName = $_POST['holiday_name'];
        $holidayDate = $_POST['holiday_date'];
        $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;

        // Validate inputs
        if (empty($holidayName) || empty($holidayDate)) {
            throw new Exception("Holiday name and date are required fields.");
        }

        // Check if holiday date already exists
        $checkStmt = $connect->prepare("SELECT COUNT(*) FROM tbl_holidays WHERE holiday_date = ?");
        $checkStmt->execute([$holidayDate]);
        $exists = $checkStmt->fetchColumn();

        if ($exists > 0) {
            throw new Exception("A holiday already exists on this date.");
        }

        // Insert into database
        $stmt = $connect->prepare("INSERT INTO tbl_holidays (holiday_name, holiday_date, is_recurring) VALUES (?, ?, ?)");
        $stmt->execute([$holidayName, $holidayDate, $isRecurring]);

        // Success message will be handled by SweetAlert
        $successMessage = "Holiday added successfully!";

        // Return JSON response for AJAX requests
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => $successMessage]);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_holidays.php");
        exit;
    } catch (Exception $e) {
        // Error message will be handled by SweetAlert
        $errorMessage = "Error adding holiday: " . $e->getMessage();

        // Return JSON response for AJAX requests
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_holidays.php");
        exit;
    }
}

// Pagination setup
$recordsPerPageOptions = array(5, 10, 25, 50, 100, 250, 500);
$defaultRecordsPerPage = 5;

// Get recordsPerPage from GET parameter, default to 5 if not set or invalid
$recordsPerPage = $defaultRecordsPerPage;
if (isset($_GET['recordsPerPage'])) {
    $requestedRecordsPerPage = intval($_GET['recordsPerPage']);
    if (in_array($requestedRecordsPerPage, $recordsPerPageOptions)) {
        $recordsPerPage = $requestedRecordsPerPage;
    }
}

// Get current page from GET parameter, default to 1 if not set or invalid
$currentPage = 1;
if (isset($_GET['page'])) {
    $requestedPage = intval($_GET['page']);
    if ($requestedPage > 0) {
        $currentPage = $requestedPage;
    }
}

// Calculate the starting record for pagination
$startFrom = ($currentPage - 1) * $recordsPerPage;

// Get total number of holidays
$totalHolidaysQuery = $connect->query("SELECT COUNT(*) as total FROM tbl_holidays");
$totalHolidaysRow = $totalHolidaysQuery->fetch(PDO::FETCH_ASSOC);
$totalHolidays = intval($totalHolidaysRow['total']);

// Calculate total pages
$totalPages = intval(ceil($totalHolidays / $recordsPerPage));

// Fetch holidays with pagination
$stmt = $connect->prepare("SELECT * FROM tbl_holidays ORDER BY holiday_date DESC LIMIT :start, :recordsPerPage");
$stmt->bindValue(':start', $startFrom, PDO::PARAM_INT);
$stmt->bindValue(':recordsPerPage', $recordsPerPage, PDO::PARAM_INT);
$stmt->execute();
$holidays = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5">
    <meta name="description" content="OJT Monitoring System is a platform designed to track and manage on-the-job training programs efficiently.">
    <meta name="author" content="Your Name or Organization">
    <meta name="theme-color" content="#FFFFFF">

    <link rel="icon" href="../assets/img/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="../assets/boxicons-master/css/boxicons.min.css">
    <link rel="stylesheet" href="../assets/node_modules/sweetalert2/dist/sweetalert2.min.css">

    <title>OJT Monitoring System - Manage Holidays</title>
    <link rel="stylesheet" href="main_style.css">

    <style>
        /* Modern UI Styles for Rows Dropdown and Pagination */
        .modern-dropdown {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modern-dropdown .dropdown-label {
            font-size: 14px;
            font-weight: 500;
            color: #555;
        }

        .modern-dropdown .btn-dropdown {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 80px;
            transition: all 0.2s ease;
        }

        .modern-dropdown .btn-dropdown:hover {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .modern-dropdown .dropdown-menu {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            padding: 4px 0;
        }

        .modern-dropdown .dropdown-item {
            padding: 6px 16px;
            font-size: 14px;
            color: #333;
            transition: all 0.2s ease;
        }

        .modern-dropdown .dropdown-item:hover {
            background: rgba(0, 123, 255, 0.1);
        }

        /* Table Styles */
        .table-responsive {
            margin-bottom: 0;
        }

        .table {
            margin-bottom: 0;
        }

        .table tfoot {
            margin-bottom: 0;
        }

        /* Modern Pagination */
        .modern-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            height: auto;
            padding: 0 4px;
        }

        .modern-pagination .page-info {
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }

        .modern-pagination .nav-buttons {
            display: flex;
            gap: 8px;
        }

        .modern-pagination .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            color: #333;
            font-size: 14px;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .modern-pagination .nav-btn:hover:not(.disabled) {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            color: #007bff;
        }

        .modern-pagination .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Holiday badge styles */
        .badge.recurring {
            background-color: #6c5ce7;
        }

        /* Custom checkbox styles */
        .custom-checkbox {
            width: 1.2em;
            height: 1.2em;
            margin-top: 0.2em;
            cursor: pointer;
        }

        .form-check-input:checked {
            background-color: #6c5ce7;
            border-color: #6c5ce7;
        }

        .form-check-label {
            cursor: pointer;
            font-weight: 500;
            padding-left: 0.5em;
        }

        @media (max-width: 425px) {

            .modern-dropdown .btn-dropdown,
            .modern-pagination .nav-btn {
                padding: 4px 8px;
                font-size: 12px;
                height: 32px;
                width: 32px;
            }

            .modern-dropdown .dropdown-label,
            .modern-pagination .page-info {
                font-size: 12px;
            }
        }

        @media (max-width: 375px) {
            .modern-pagination .nav-btn {
                padding: 3px 6px;
                font-size: 11px;
                height: 30px;
                width: 30px;
            }

            .modern-pagination .page-info {
                font-size: 11px;
            }
        }

        @media (max-width: 320px) {
            .modern-dropdown .btn-dropdown {
                min-width: 60px;
                padding: 3px 6px;
                font-size: 11px;
                height: 28px;
            }

            .modern-pagination .nav-btn {
                padding: 2px 5px;
                font-size: 10px;
                height: 28px;
                width: 28px;
            }

            .modern-pagination .page-info {
                font-size: 10px;
            }
        }
    </style>
</head>

<body>
    <?php include 'sidebar.php' ?>

    <div class="main-content" id="main-content">
        <?php include 'navbar_header.php' ?>
        <div class="content" style="border-radius: 20px; margin-left: 10px; margin-right: 10px;">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Manage Holidays</li>
                </ol>
            </nav>
            <main>
                <!-- SweetAlert handles success/error messages -->

                <div class="row">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mt-3 mb-3">
                                <div>
                                    <h2 style="font-size: 16px;">Non-Working Holidays</h2>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="modern-dropdown me-2">
                                        <span class="dropdown-label">Rows:</span>
                                        <div class="btn-group">
                                            <button class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                <?php echo $recordsPerPage; ?>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php foreach ($recordsPerPageOptions as $option): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="manage_holidays.php?recordsPerPage=<?php echo intval($option); ?>&page=1">
                                                            <?php echo intval($option); ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="me-2" style="width: 200px;">
                                        <input type="search" id="searchHoliday" class="form-control" placeholder="Search holiday..." />
                                    </div>
                                    <button type="button" class="btn btn-md btn-primary" style="text-transform: capitalize; font-size: 12px;" data-mdb-modal-init data-mdb-target="#addHolidayModal">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="table-responsive card">
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">#</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Holiday Name</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Date</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Type</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="holidaysTableBody">

                                        <?php if (count($holidays) > 0): ?>
                                            <?php
                                            // Calculate counter for row numbering
                                            $counter = $startFrom + 1; // startFrom is already calculated correctly above

                                            foreach ($holidays as $holiday):
                                                // Format the date
                                                $holidayDate = new DateTime($holiday['holiday_date']);
                                                $formattedDate = $holidayDate->format('F j, Y');
                                            ?>
                                                <tr>
                                                    <td class="text-center"><?php echo $counter++; ?></td>
                                                    <td class="text-center"><?php echo htmlspecialchars($holiday['holiday_name']); ?></td>
                                                    <td class="text-center"><?php echo $formattedDate; ?></td>
                                                    <td class="text-center">
                                                        <?php if ($holiday['is_recurring'] == 1): ?>
                                                            <span class="badge recurring">Recurring</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-primary">One-time</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="<?php echo $holiday['id']; ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger btn-delete" data-id="<?php echo $holiday['id']; ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center">No holidays found</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">#</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Holiday Name</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Date</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Type</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Actions</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Modern Pagination Controls -->
                            <div class="modern-pagination">
                                <!-- Page Information (Left) -->
                                <div class="page-info">
                                    <?php
                                    // Calculate the range of rows being displayed - ensure all values are integers
                                    $startRow = intval($startFrom) + 1; // Use startFrom which is already calculated correctly
                                    $endRow = min($startRow + intval($recordsPerPage) - 1, intval($totalHolidays));

                                    // Handle case when there are no records
                                    if (intval($totalHolidays) === 0) {
                                        $startRow = 0;
                                        $endRow = 0;
                                    }
                                    ?>
                                    Result of <?php echo $startRow; ?>-<?php echo $endRow; ?> of <?php echo $totalHolidays; ?>
                                </div>

                                <!-- Navigation Buttons (Right) -->
                                <div class="nav-buttons">
                                    <!-- Previous Button -->
                                    <?php
                                    $currentPageInt = intval($currentPage); // Ensure currentPage is an integer
                                    $recordsPerPageInt = intval($recordsPerPage); // Ensure recordsPerPage is an integer

                                    // Create the URL for the previous page with recordsPerPage parameter
                                    $prevPageUrl = 'manage_holidays.php?page=' . ($currentPageInt - 1) . '&recordsPerPage=' . $recordsPerPageInt;
                                    ?>
                                    <a class="nav-btn <?php echo $currentPageInt <= 1 ? 'disabled' : ''; ?>"
                                        href="<?php echo $currentPageInt > 1 ? $prevPageUrl : 'javascript:void(0)'; ?>"
                                        aria-label="Previous"
                                        <?php echo $currentPageInt <= 1 ? 'onclick="return false;"' : ''; ?>>
                                        <i class="fa fa-chevron-left"></i>
                                    </a>

                                    <!-- Next Button -->
                                    <?php
                                    $totalPagesInt = intval($totalPages); // Ensure totalPages is an integer

                                    // Create the URL for the next page with recordsPerPage parameter
                                    $nextPageUrl = 'manage_holidays.php?page=' . ($currentPageInt + 1) . '&recordsPerPage=' . $recordsPerPageInt;
                                    ?>
                                    <a class="nav-btn <?php echo $currentPageInt >= $totalPagesInt ? 'disabled' : ''; ?>"
                                        href="<?php echo $currentPageInt < $totalPagesInt ? $nextPageUrl : 'javascript:void(0)'; ?>"
                                        aria-label="Next"
                                        <?php echo $currentPageInt >= $totalPagesInt ? 'onclick="return false;"' : ''; ?>>
                                        <i class="fa fa-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        <!-- Footer -->
        <footer class="bg-link text-center ">

            <!-- Copyright -->
            <div class="text-center p-2 mt-5" style="background-color: #F5F5F9;">
                © 2025 Developed by:
                <a class="text-dark" href="#">John Lloyd Caban</a>
            </div>
            <!-- Copyright -->

        </footer>
        <!-- Footer -->
    </div>

    <!-- ADD HOLIDAY MODAL -->
    <div class="modal fade" id="addHolidayModal" tabindex="-1" aria-labelledby="addHolidayModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="border-radius: 12px; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                <div class="modal-header bg-primary text-white" style="padding: 16px 24px;">
                    <h5 class="modal-title" id="addHolidayModalLabel" style="font-weight: 600;">Add New Holiday</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 24px;">
                    <form id="addHolidayForm" method="POST">
                        <input type="hidden" name="add_holiday" value="1">

                        <div class="mb-4">
                            <label for="holiday_name" class="form-label">Holiday Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-day"></i></span>
                                <input type="text" class="form-control" id="holiday_name" name="holiday_name" required placeholder="Enter holiday name">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="holiday_date" class="form-label">Holiday Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="date" class="form-control" id="holiday_date" name="holiday_date" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label d-block">Holiday Type</label>
                            <div class="form-check">
                                <input class="form-check-input custom-checkbox" type="checkbox" id="is_recurring" name="is_recurring">
                                <label class="form-check-label" for="is_recurring">
                                    Recurring Holiday (occurs every year)
                                </label>
                            </div>
                            <small class="text-muted">Check this box for holidays that repeat annually</small>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-light me-2" data-mdb-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Save Holiday</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- EDIT HOLIDAY MODAL -->
    <div class="modal fade" id="editHolidayModal" tabindex="-1" aria-labelledby="editHolidayModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="border-radius: 12px; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                <div class="modal-header bg-warning text-dark" style="padding: 16px 24px;">
                    <h5 class="modal-title" id="editHolidayModalLabel" style="font-weight: 600;">Edit Holiday</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 24px;">
                    <form id="editHolidayForm" method="POST">
                        <input type="hidden" name="edit_holiday" value="1">
                        <input type="hidden" name="id" id="edit_holiday_id">

                        <div class="mb-4">
                            <label for="edit_holiday_name" class="form-label">Holiday Name</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-day"></i></span>
                                <input type="text" class="form-control" id="edit_holiday_name" name="holiday_name" required placeholder="Enter holiday name">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="edit_holiday_date" class="form-label">Holiday Date</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="date" class="form-control" id="edit_holiday_date" name="holiday_date" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label d-block">Holiday Type</label>
                            <div class="form-check">
                                <input class="form-check-input custom-checkbox" type="checkbox" id="edit_is_recurring" name="is_recurring">
                                <label class="form-check-label" for="edit_is_recurring">
                                    Recurring Holiday (occurs every year)
                                </label>
                            </div>
                            <small class="text-muted">Check this box for holidays that repeat annually</small>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="button" class="btn btn-light me-2" data-mdb-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-warning">Update Holiday</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- MDB -->
    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>
    <script src="main_script.js"></script>

    <!-- Holiday Management Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add Holiday Form Submission
            const addHolidayForm = document.getElementById('addHolidayForm');
            if (addHolidayForm) {
                addHolidayForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Create FormData object
                    const formData = new FormData(this);
                    formData.append('ajax', '1');

                    // Send AJAX request
                    fetch('manage_holidays.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Show success message
                                Swal.fire({
                                    title: 'Success!',
                                    text: data.message,
                                    icon: 'success',
                                    confirmButtonText: 'OK'
                                }).then(() => {
                                    // Close modal and refresh page
                                    const modal = document.getElementById('addHolidayModal');
                                    const modalInstance = mdb.Modal.getInstance(modal);
                                    modalInstance.hide();

                                    // Reset form
                                    addHolidayForm.reset();

                                    // Reload page to show new holiday
                                    window.location.reload();
                                });
                            } else {
                                // Show error message
                                Swal.fire({
                                    title: 'Error!',
                                    text: data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'Error!',
                                text: 'An unexpected error occurred. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        });
                });
            }

            // Search functionality
            const searchHoliday = document.getElementById('searchHoliday');
            if (searchHoliday) {
                searchHoliday.addEventListener('input', function() {
                    const searchValue = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('#holidaysTableBody tr');

                    tableRows.forEach(row => {
                        const holidayName = row.cells[1].textContent.toLowerCase();
                        const holidayDate = row.cells[2].textContent.toLowerCase();

                        if (holidayName.includes(searchValue) || holidayDate.includes(searchValue)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }

            // Edit Holiday Form Submission
            const editHolidayForm = document.getElementById('editHolidayForm');
            if (editHolidayForm) {
                editHolidayForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Create FormData object
                    const formData = new FormData(this);
                    formData.append('ajax', '1');

                    // Send AJAX request
                    fetch('manage_holidays.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Show success message
                                Swal.fire({
                                    title: 'Success!',
                                    text: data.message,
                                    icon: 'success',
                                    confirmButtonText: 'OK'
                                }).then(() => {
                                    // Close modal and refresh page
                                    const modal = document.getElementById('editHolidayModal');
                                    const modalInstance = mdb.Modal.getInstance(modal);
                                    modalInstance.hide();

                                    // Reset form
                                    editHolidayForm.reset();

                                    // Reload page to show updated holiday
                                    window.location.reload();
                                });
                            } else {
                                // Show error message
                                Swal.fire({
                                    title: 'Error!',
                                    text: data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'Error!',
                                text: 'An unexpected error occurred. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        });
                });
            }

            // Initialize edit buttons
            const editButtons = document.querySelectorAll('.btn-edit');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const holidayId = this.getAttribute('data-id');

                    // Fetch holiday data
                    fetch(`manage_holidays.php?get_holiday=1&id=${holidayId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Populate form with holiday data
                                document.getElementById('edit_holiday_id').value = data.data.id;
                                document.getElementById('edit_holiday_name').value = data.data.holiday_name;
                                document.getElementById('edit_holiday_date').value = data.data.holiday_date;
                                document.getElementById('edit_is_recurring').checked = data.data.is_recurring == 1;

                                // Show modal
                                const modal = document.getElementById('editHolidayModal');
                                const modalInstance = new mdb.Modal(modal);
                                modalInstance.show();
                            } else {
                                // Show error message
                                Swal.fire({
                                    title: 'Error!',
                                    text: data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'Error!',
                                text: 'An unexpected error occurred. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        });
                });
            });

            // Initialize delete buttons
            const deleteButtons = document.querySelectorAll('.btn-delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const holidayId = this.getAttribute('data-id');
                    const holidayRow = this.closest('tr');
                    const holidayName = holidayRow.cells[1].textContent;

                    // Show confirmation dialog
                    Swal.fire({
                        title: 'Are you sure?',
                        text: `Do you want to delete the holiday "${holidayName}"? This action cannot be undone.`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Create FormData object
                            const formData = new FormData();
                            formData.append('delete_holiday', '1');
                            formData.append('id', holidayId);

                            // Send AJAX request
                            fetch('manage_holidays.php', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        // Show success message
                                        Swal.fire({
                                            title: 'Deleted!',
                                            text: data.message,
                                            icon: 'success',
                                            confirmButtonText: 'OK'
                                        }).then(() => {
                                            // Remove row from table or reload page
                                            holidayRow.remove();

                                            // If no more rows, show 'No holidays found' message
                                            const tableRows = document.querySelectorAll('#holidaysTableBody tr');
                                            if (tableRows.length === 0) {
                                                const noHolidaysRow = document.createElement('tr');
                                                noHolidaysRow.innerHTML = '<td colspan="5" class="text-center">No holidays found</td>';
                                                document.getElementById('holidaysTableBody').appendChild(noHolidaysRow);
                                            }
                                        });
                                    } else {
                                        // Show error message
                                        Swal.fire({
                                            title: 'Error!',
                                            text: data.message,
                                            icon: 'error',
                                            confirmButtonText: 'OK'
                                        });
                                    }
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    Swal.fire({
                                        title: 'Error!',
                                        text: 'An unexpected error occurred. Please try again.',
                                        icon: 'error',
                                        confirmButtonText: 'OK'
                                    });
                                });
                        }
                    });
                });
            });
        });
    </script>
</body>

</html>
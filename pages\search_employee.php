<?php
include 'database/db_connection.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $employeeId = $_POST['employeeId'];

    // Prepare and execute the SQL query
    $stmt = $connect->prepare("SELECT * FROM tbl_employee WHERE employee_id = :employeeId");
    $stmt->bindParam(':employeeId', $employeeId);
    $stmt->execute();

    $employee = $stmt->fetch(PDO::FETCH_ASSOC);

    // Output the necessary HTML structure and include SweetAlert
    echo '<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>
              <title>Search Result</title>
          </head>
          <body>';

    if ($employee) {
        // Employee found, prepare the response
        echo "<script>
                Swal.fire({
                    icon: 'success',
                    title: 'Employee Found',
                    text: 'Employee ID: " . htmlspecialchars($employee['employee_id'], ENT_QUOTES, 'UTF-8') . "',
                    confirmButtonText: 'OK'
                }).then(function() {
                    window.location.href = 'landing_page.php';
                });
              </script>";
    } else {
        // Employee not found, prepare the response
        echo "<script>
                Swal.fire({
                    icon: 'error',
                    title: 'Employee Not Found',
                    text: 'No employee with the ID " . htmlspecialchars($employeeId, ENT_QUOTES, 'UTF-8') . " was found.',
                    confirmButtonText: 'OK'
                }).then(function() {
                    window.location.href = 'landing_page.php';
                });
              </script>";
    }

    echo '</body>
          </html>';
}

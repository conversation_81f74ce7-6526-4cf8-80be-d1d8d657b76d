<?php
/**
 * Auto Mark Absent Script
 *
 * This script automatically marks employees as absent if they haven't logged attendance for the day.
 * It is designed to be run via cron job or manually from the admin interface.
 */

// Define a flag to check if this script is being called directly or from the web
$isWebRequest = !empty($_SERVER['HTTP_USER_AGENT']) || (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'GET');

// Set timezone
date_default_timezone_set('Asia/Manila');

/**
 * Output a message to the appropriate destination based on request type
 *
 * @param string $message The message to output
 * @param string $type The type of message (info, success, warning, error, summary)
 * @return void
 */
function outputMessage($message, $type = 'info') {
    global $isWebRequest;

    if ($isWebRequest) {
        echo "<script>addLogEntry(" . json_encode($message) . ", " . json_encode($type) . ");</script>\n";
        // Make sure the output is flushed immediately
        flush();
        ob_flush();
    } else {
        // Set console colors for CLI output
        $colors = [
            'info' => "\033[0;36m", // Cyan
            'success' => "\033[0;32m", // Green
            'warning' => "\033[0;33m", // Yellow
            'error' => "\033[0;31m", // Red
            'summary' => "\033[1;35m", // Purple bold
        ];
        $reset = "\033[0m";

        echo $colors[$type] . $message . $reset . PHP_EOL;
    }
}

// If this is a web request, output HTML interface
if ($isWebRequest) {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Absent Marking System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .card-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .output-container {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-summary { background-color: #e2e3e5; color: #383d41; font-weight: bold; }
        .spinner-border-sm { margin-right: 10px; }
        .btn-run { background: linear-gradient(45deg, #28a745, #20c997); border: none; }
        .btn-run:hover { background: linear-gradient(45deg, #218838, #1fa080); }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-clock me-2"></i>Auto Absent Marking System</h3>
                        <p class="mb-0">Automatically mark employees as absent for today</p>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <strong>Current Date:</strong> <?php echo date('F j, Y'); ?>
                            </div>
                            <div class="col-sm-6">
                                <strong>Day:</strong> <?php echo date('l'); ?>
                            </div>
                        </div>

                        <div class="text-center mb-3">
                            <button id="runProcess" class="btn btn-run btn-lg px-4">
                                <i class="fas fa-play me-2"></i>Run Absent Marking Process
                            </button>
                        </div>

                        <div id="outputContainer" class="output-container" style="display: none;">
                            <h5><i class="fas fa-terminal me-2"></i>Process Output:</h5>
                            <div id="logOutput"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // SweetAlert2 Toast Mixin
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        let logMessages = [];
        let processRunning = false;

        function addLogEntry(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function showToast(icon, title) {
            Toast.fire({
                icon: icon,
                title: title
            });
        }

        document.getElementById('runProcess').addEventListener('click', function() {
            if (processRunning) return;

            processRunning = true;
            const button = this;
            const originalText = button.innerHTML;

            button.innerHTML = '<span class="spinner-border spinner-border-sm"></span>Processing...';
            button.disabled = true;

            document.getElementById('outputContainer').style.display = 'block';
            document.getElementById('logOutput').innerHTML = '';

            showToast('info', 'Starting absent marking process...');

            // Simulate process start
            addLogEntry('Initializing auto absent marking system...', 'info');

            // Continue with PHP processing
            setTimeout(() => {
                window.location.href = window.location.href + '?run=1';
            }, 1000);
        });

        // Show initial toast if process was run
        <?php if (isset($_GET['run'])): ?>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('outputContainer').style.display = 'block';
            showToast('success', 'Process initiated successfully');
        });
        <?php endif; ?>
    </script>
</body>
</html>
<?php
    if (!isset($_GET['run'])) {
        exit(); // Don't run the process if just displaying the interface
    }
}

// Database connection
try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    outputMessage("Database connection established.", 'success');
} catch (PDOException $e) {
    outputMessage("Database connection failed: " . $e->getMessage(), 'error');
    if ($isWebRequest) {
        echo "<script>showToast('error', 'Database connection failed!');</script>\n";
        echo "<script>document.getElementById('runProcess').innerHTML = '<i class=\"fas fa-play me-2\"></i>Run Absent Marking Process'; document.getElementById('runProcess').disabled = false;</script>\n";
    }
    exit();
}

// Check if today is a weekend
$today = date('Y-m-d');
$dayOfWeek = date('N', strtotime($today)); // 1 (Monday) to 7 (Sunday)
$isWeekend = ($dayOfWeek >= 6); // 6 = Saturday, 7 = Sunday

if ($isWeekend) {
    outputMessage("Today is a weekend. Skipping absence marking.", 'warning');
    if ($isWebRequest) {
        echo "<script>showToast('warning', 'Weekend detected - process skipped');</script>\n";
        echo "<script>document.getElementById('runProcess').innerHTML = '<i class=\"fas fa-play me-2\"></i>Run Absent Marking Process'; document.getElementById('runProcess').disabled = false;</script>\n";
    }
    exit();
}

// Check if today is a holiday
function isHoliday($connect, $date) {
    $query = "
        SELECT
            id,
            holiday_name,
            holiday_date,
            is_recurring
        FROM
            tbl_holidays
        WHERE
            holiday_date = :date
            OR (
                is_recurring = 1
                AND MONTH(holiday_date) = MONTH(:date)
                AND DAY(holiday_date) = DAY(:date)
            )
    ";

    $stmt = $connect->prepare($query);
    $stmt->bindParam(':date', $date);
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

$holiday = isHoliday($connect, $today);
if ($holiday) {
    outputMessage("Today (" . date('F j, Y') . ") is a holiday: " . $holiday['holiday_name'] . ". Skipping absence marking.", 'warning');
    if ($isWebRequest) {
        echo "<script>showToast('warning', 'Holiday detected - process skipped');</script>\n";
        echo "<script>document.getElementById('runProcess').innerHTML = '<i class=\"fas fa-play me-2\"></i>Run Absent Marking Process'; document.getElementById('runProcess').disabled = false;</script>\n";
    }
    exit();
}

outputMessage("Processing absences for " . date('F j, Y') . "...", 'info');

// Get all active employees (excluding admins and those who completed required hours)
$employeeQuery = "
    SELECT
        tbl_emp_id,
        employee_id,
        CONCAT(first_name, ' ', surname) AS employee_name,
        no_hours_required
    FROM
        tbl_employee
    WHERE
        user_role = 'Employee'
";

$employeeStmt = $connect->prepare($employeeQuery);
$employeeStmt->execute();
$employees = $employeeStmt->fetchAll(PDO::FETCH_ASSOC);

outputMessage("Found " . count($employees) . " active employees to check.");

$absentCount = 0;
$skippedCount = 0;
$alreadyMarkedCount = 0;

foreach ($employees as $employee) {
    $empId = $employee['tbl_emp_id'];
    $empName = $employee['employee_name'];
    $requiredHours = floatval($employee['no_hours_required']);

    outputMessage("Checking employee: $empName (ID: $empId)...", 'info');

    // Check if employee has completed their required hours
    if ($requiredHours > 0) {
        $hoursQuery = "
            SELECT SUM(total_daily_hours) AS total_hours
            FROM tbl_attendance
            WHERE tbl_emp_id = :emp_id AND is_absent = 0
        ";
        $hoursStmt = $connect->prepare($hoursQuery);
        $hoursStmt->bindParam(':emp_id', $empId);
        $hoursStmt->execute();
        $hoursResult = $hoursStmt->fetch(PDO::FETCH_ASSOC);
        $totalHours = floatval($hoursResult['total_hours'] ?? 0);

        if ($totalHours >= $requiredHours) {
            outputMessage("  - SKIPPED: Employee has completed required $requiredHours hours (current: $totalHours hours).", 'warning');
            $skippedCount++;
            continue;
        }
    }

    // Check if employee already has an attendance record for today
    $checkQuery = "
        SELECT * FROM tbl_attendance
        WHERE tbl_emp_id = :emp_id AND DATE(attendance_date) = :today
    ";
    $checkStmt = $connect->prepare($checkQuery);
    $checkStmt->bindParam(':emp_id', $empId);
    $checkStmt->bindParam(':today', $today);
    $checkStmt->execute();
    $attendanceRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if ($attendanceRecord) {
        outputMessage("  - SKIPPED: Employee already has an attendance record for today.", 'warning');
        if ($attendanceRecord['is_absent'] == 1) {
            outputMessage("  - NOTE: Employee is already marked as absent.", 'warning');
            $alreadyMarkedCount++;
        }
        $skippedCount++;
        continue;
    }

    // Mark employee as absent
    try {
        $insertQuery = "
            INSERT INTO tbl_attendance
                (attendance_date, tbl_emp_id, is_absent)
            VALUES
                (:today, :emp_id, 1)
        ";
        $insertStmt = $connect->prepare($insertQuery);
        $insertStmt->bindParam(':today', $today);
        $insertStmt->bindParam(':emp_id', $empId);
        $insertStmt->execute();

        outputMessage("  - SUCCESS: Marked as absent for today.", 'success');
        $absentCount++;

    } catch (PDOException $e) {
        outputMessage("  - ERROR: Failed to mark as absent: " . $e->getMessage(), 'error');
    }
}

outputMessage("\nSummary:", 'summary');
outputMessage("Total employees processed: " . count($employees), 'summary');
outputMessage("Employees marked as absent: $absentCount", 'summary');
outputMessage("Employees skipped: $skippedCount", 'summary');
outputMessage("Employees already marked absent: $alreadyMarkedCount", 'summary');
outputMessage("\nProcess completed at " . date('Y-m-d H:i:s'), 'summary');

if ($isWebRequest) {
    echo "<script>showToast('success', 'Process completed successfully!');</script>\n";
    echo "<script>document.getElementById('runProcess').innerHTML = '<i class=\"fas fa-check me-2\"></i>Process Completed'; document.getElementById('runProcess').disabled = false;</script>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test SweetAlert Loading - OJT Monitoring System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>SweetAlert Loading Modal Test</h1>

        <div class="info">
            <h3>Test the New Loading Experience:</h3>
            <p>This page tests the new SweetAlert loading modals that replace the old "No Employee Data Available" messages.</p>
        </div>

        <div class="success">
            <h4>What's New:</h4>
            <ul>
                <li><strong>Loading Modal:</strong> Shows "Loading Employee Data" with spinner</li>
                <li><strong>Success Modal:</strong> Shows "Data Loaded Successfully!" with auto-close</li>
                <li><strong>Welcome Modal:</strong> Shows "Welcome Back!" for cached data</li>
                <li><strong>Error Modal:</strong> Shows detailed error messages with retry options</li>
                <li><strong>No Data Modal:</strong> Shows warning with navigation options</li>
            </ul>
        </div>

        <h3>Test Different Loading Scenarios:</h3>

        <button class="button" onclick="testLoadingModal()">
            <i class="fas fa-spinner me-2"></i>Test Loading Modal
        </button>

        <button class="button" onclick="testSuccessToast()">
            <i class="fas fa-check me-2"></i>Test Success Modal
        </button>

        <button class="button" onclick="testErrorModal()">
            <i class="fas fa-exclamation-triangle me-2"></i>Test Error Modal
        </button>

        <button class="button" onclick="testNoDataModal()">
            <i class="fas fa-question-circle me-2"></i>Test No Data Modal
        </button>

        <button class="button" onclick="testFullSequence()">
            <i class="fas fa-play me-2"></i>Test Full Loading Sequence
        </button>

        <h3>Navigation:</h3>
        <a href="login.php">Go to Login</a> |
        <a href="employee_dashboard.php">Go to Employee Dashboard</a> |
        <a href="test_auto_load.php">Auto Load Test</a>
    </div>

    <script>
        function testLoadingModal() {
            Swal.fire({
                title: 'Loading Your Profile',
                text: 'Please wait while we prepare your dashboard...',
                icon: 'info',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Auto close after 3 seconds
            setTimeout(() => {
                Swal.close();
            }, 3000);
        }

        function testSuccessToast() {
            Swal.fire({
                title: 'Profile Loaded Successfully!',
                html: 'Your dashboard is now ready to use.',
                icon: 'success',
                timer: 2000,
                timerProgressBar: true,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
                willClose: () => {
                    console.log("Success modal closed");
                }
            });
        }

        function testErrorModal() {
            Swal.fire({
                title: 'Error Loading Data',
                text: 'There was a problem loading the employee information.',
                icon: 'error',
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#dc3545'
            });
        }

        function testNoDataModal() {
            Swal.fire({
                title: 'No Employee Data Available',
                text: 'Unable to load employee information. Please return to the employee list and select an employee to view.',
                icon: 'warning',
                confirmButtonText: 'Go to Employee List',
                confirmButtonColor: '#007bff',
                showCancelButton: true,
                cancelButtonText: 'Retry',
                cancelButtonColor: '#6c757d'
            }).then((result) => {
                if (result.isConfirmed) {
                    alert('Would redirect to: manage_employee.php');
                } else if (result.dismiss === Swal.DismissReason.cancel) {
                    alert('Would reload the page');
                }
            });
        }

        function testFullSequence() {
            // Step 1: Show loading
            Swal.fire({
                title: 'Loading Your Profile',
                text: 'Please wait while we prepare your dashboard...',
                icon: 'info',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Step 2: Simulate loading time
            setTimeout(() => {
                // Step 3: Show success
                Swal.fire({
                    title: 'Profile Loaded Successfully!',
                    html: 'Your dashboard is now ready to use.',
                    icon: 'success',
                    timer: 2000,
                    timerProgressBar: true,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                    willClose: () => {
                        console.log("Full sequence success modal closed");
                    }
                });
            }, 2000);
        }

        // Show welcome message on page load
        window.addEventListener('load', function() {
            Swal.fire({
                title: 'SweetAlert Test Ready!',
                html: 'Click the buttons above to test different loading scenarios.',
                icon: 'info',
                timer: 2500,
                timerProgressBar: true,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
                willClose: () => {
                    console.log("Welcome message closed");
                }
            });
        });
    </script>
</body>
</html>

<?php
include 'database/db_connection.php';

// Get environment information
$env_info = function_exists('getEnvironmentInfo') ? getEnvironmentInfo() : [
    'base_url' => 'Function not available',
    'is_ngrok' => false,
    'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
    'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown'
];

// Check if ngrok is running
$is_ngrok_running = $env_info['is_ngrok'];

// Get the current URL
$current_url = $env_info['base_url'];

// Get the ngrok URL if available
$ngrok_url = $is_ngrok_running ? $current_url : 'ngrok is not running';

// Get the local URL
$local_url = str_replace($env_info['http_host'], 'localhost', $current_url);
?>
<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5">
    <meta name="description" content="OJT Monitoring System ngrok configuration">

    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="../assets/boxicons-master/css/boxicons.min.css">

    <title>ngrok Configuration - OJT Monitoring System</title>
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --text-color: #333;
            --light-text: #666;
            --bg-color: #ffffff;
            --card-bg: rgba(255, 255, 255, 0.7);
            --input-bg: rgba(255, 255, 255, 0.8);
            --border-radius: 16px;
            --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Roboto', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .page-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
        }

        .header-title {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
            color: var(--text-color);
        }

        .header-address {
            font-size: 0.85rem;
            color: var(--light-text);
            margin-bottom: 0;
        }

        .logo-container {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image {
            max-width: 100%;
            max-height: 100%;
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            border: 1px solid rgba(200, 200, 200, 0.3);
            box-shadow: var(--box-shadow);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: rgba(0, 0, 0, 0.03);
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            padding: 1rem 1.25rem;
            border-top-left-radius: var(--border-radius);
            border-top-right-radius: var(--border-radius);
        }

        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 20px;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }

        .url-display {
            word-break: break-all;
            font-family: monospace;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 10px 0;
        }

        .copy-btn {
            cursor: pointer;
        }

        .info-table {
            width: 100%;
            margin-top: 20px;
        }

        .info-table td {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }

        .info-table tr:last-child td {
            border-bottom: none;
        }

        .info-label {
            font-weight: bold;
            width: 30%;
        }

        .step-card {
            margin-bottom: 1rem;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }

        .modern-footer {
            margin-top: auto;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 1rem 0;
        }

        .footer-content {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .footer-logo {
            margin-right: 1rem;
        }

        .footer-logo-img {
            width: 30px;
            height: 30px;
        }

        .footer-info {
            text-align: center;
        }

        .copyright, .developer {
            margin-bottom: 0;
            font-size: 0.85rem;
            color: var(--light-text);
        }

        .developer-link {
            color: var(--primary-color);
            text-decoration: none;
        }

        .developer-link:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .header-title {
                font-size: 1.2rem;
            }

            .header-address {
                font-size: 0.75rem;
            }
        }
    </style>
</head>

<body>
    <div class="page-header">
        <div class="container py-2">
            <div class="row align-items-center">
                <div class="col-auto">
                    <div class="logo-container">
                        <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="img-fluid logo-image">
                    </div>
                </div>
                <div class="col">
                    <div class="header-info">
                        <h3 class="header-title">DICT Surigao del Norte Provincial Office</h3>
                        <p class="header-address">Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">ngrok Status</h5>
                        <?php if ($is_ngrok_running): ?>
                            <span class="status-badge status-active">Active</span>
                        <?php else: ?>
                            <span class="status-badge status-inactive">Inactive</span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This page shows the current status of your ngrok connection and provides information about your environment.
                        </div>

                        <h5 class="mt-4">Current URL</h5>
                        <div class="url-display d-flex align-items-center">
                            <span class="me-2"><?php echo htmlspecialchars($current_url); ?></span>
                            <i class="fas fa-copy copy-btn" data-mdb-toggle="tooltip" title="Copy URL"
                               onclick="copyToClipboard('<?php echo htmlspecialchars($current_url); ?>')"></i>
                        </div>

                        <h5 class="mt-4">Environment Information</h5>
                        <table class="info-table">
                            <tr>
                                <td class="info-label">ngrok Active</td>
                                <td><?php echo $is_ngrok_running ? 'Yes' : 'No'; ?></td>
                            </tr>
                            <tr>
                                <td class="info-label">Server Name</td>
                                <td><?php echo htmlspecialchars($env_info['server_name']); ?></td>
                            </tr>
                            <tr>
                                <td class="info-label">HTTP Host</td>
                                <td><?php echo htmlspecialchars($env_info['http_host']); ?></td>
                            </tr>
                            <tr>
                                <td class="info-label">Local URL</td>
                                <td><?php echo htmlspecialchars($local_url); ?></td>
                            </tr>
                            <?php if ($is_ngrok_running): ?>
                            <tr>
                                <td class="info-label">ngrok URL</td>
                                <td><?php echo htmlspecialchars($ngrok_url); ?></td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">How to Use ngrok</h5>
                    </div>
                    <div class="card-body">
                        <div class="step-card">
                            <h6><span class="step-number">1</span>Download and Install ngrok</h6>
                            <p>Visit <a href="https://ngrok.com/download" target="_blank">ngrok.com/download</a> to download ngrok for your operating system.</p>
                        </div>

                        <div class="step-card">
                            <h6><span class="step-number">2</span>Sign Up for an Account</h6>
                            <p>Create a free account at <a href="https://dashboard.ngrok.com/signup" target="_blank">ngrok.com/signup</a> to get your authtoken.</p>
                        </div>

                        <div class="step-card">
                            <h6><span class="step-number">3</span>Configure Your Authtoken</h6>
                            <p>After signing up, run the following command to configure your authtoken:</p>
                            <div class="code-block">ngrok config add-authtoken YOUR_AUTH_TOKEN</div>
                        </div>

                        <div class="step-card">
                            <h6><span class="step-number">4</span>Start ngrok</h6>
                            <p>Run the following command to expose your local web server (assuming WAMP is running on port 80):</p>
                            <div class="code-block">ngrok http 80</div>
                        </div>

                        <div class="step-card">
                            <h6><span class="step-number">5</span>Access Your Application</h6>
                            <p>After starting ngrok, you'll see a URL (e.g., https://abc123.ngrok.io) that you can use to access your application from anywhere.</p>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> The free version of ngrok will generate a new URL each time you restart it, and sessions last for 2 hours.
                        </div>

                        <div class="mt-4">
                            <a href="<?php echo isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'Admin' ? 'dashboard.php' : 'employee_dashboard.php'; ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="modern-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="footer-logo-img">
                </div>
                <div class="footer-info">
                    <p class="copyright">&copy; <?php echo date('Y'); ?> OJT Monitoring System</p>
                    <p class="developer">Developed by: <a href="#" class="developer-link">John Lloyd Caban</a></p>
                </div>
            </div>
        </div>
    </footer>

    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>
    <script>
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-mdb-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new mdb.Tooltip(tooltipTriggerEl);
            });
        });

        // Copy URL to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                Swal.fire({
                    title: 'Copied!',
                    text: 'URL has been copied to clipboard',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }, function(err) {
                console.error('Could not copy text: ', err);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to copy URL',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            });
        }
    </script>
</body>

</html>

<?php
// manage_supervisor.php

include 'database/db_connection.php';

// Define base paths for assets
$base_url = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http";
$base_url .= "://" . $_SERVER['HTTP_HOST'];
$base_url .= str_replace(basename($_SERVER['SCRIPT_NAME']), "", $_SERVER['SCRIPT_NAME']);
$assets_path = dirname($base_url) . '/assets/';
$default_profile_img = $assets_path . 'img/default_account.png';

// Add Supervisor
if (isset($_POST['addSupervisor'])) {
    $name = $_POST['name'];
    $gmail = $_POST['gmail'];
    $password = $_POST['password'];
    $department = $_POST['department'];
    $user_role = $_POST['user_role'] ?? 'Admin'; // Default to Admin if not provided
    $created_at = date('Y-m-d H:i:s');
    $updated_at = date('Y-m-d H:i:s');

    // Check if the gmail already exists
    $checkStmt = $connect->prepare("SELECT COUNT(*) FROM immediate_supervisor WHERE gmail = ?");
    $checkStmt->execute([$gmail]);
    $count = $checkStmt->fetchColumn();

    if ($count > 0) {
        echo json_encode(['status' => 'error', 'message' => 'Email already exists']);
        exit();
    }

    // Handle profile image upload
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $profile_image = file_get_contents($_FILES['profile_image']['tmp_name']);
    } else {
        $profile_image = null;
    }

    // Hash the password if provided
    $hashedPassword = !empty($password) ? password_hash($password, PASSWORD_DEFAULT) : null;

    try {
        $stmt = $connect->prepare("INSERT INTO immediate_supervisor (profile_image, name, gmail, password, department, user_role, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([$profile_image, $name, $gmail, $hashedPassword, $department, $user_role, $created_at, $updated_at]);

        if ($result) {
            echo json_encode(['status' => 'success', 'message' => 'Supervisor added successfully']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to add supervisor']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit();
}

// Update Supervisor
if (isset($_POST['updateSupervisor'])) {
    $supervisor_id = $_POST['supervisor_id'];
    $name = $_POST['name'];
    $gmail = $_POST['gmail'];
    $password = $_POST['password'];
    $department = $_POST['department'];
    $user_role = $_POST['user_role'] ?? 'Admin'; // Default to Admin if not provided
    $updated_at = date('Y-m-d H:i:s');

    try {
        // Check if the email exists for other supervisors
        $checkStmt = $connect->prepare("SELECT COUNT(*) FROM immediate_supervisor WHERE gmail = ? AND supervisor_id != ?");
        $checkStmt->execute([$gmail, $supervisor_id]);
        $count = $checkStmt->fetchColumn();

        if ($count > 0) {
            echo json_encode(['status' => 'error', 'message' => 'Email already exists for another supervisor']);
            exit();
        }

        // Handle profile image upload
        $updateProfileImage = false;
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $profile_image = file_get_contents($_FILES['profile_image']['tmp_name']);
            $updateProfileImage = true;
        }

        // Build the SQL query based on what needs to be updated
        if (!empty($password) && $updateProfileImage) {
            // Update password and profile image
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $connect->prepare("UPDATE immediate_supervisor SET profile_image = ?, name = ?, gmail = ?, password = ?, department = ?, user_role = ?, updated_at = ? WHERE supervisor_id = ?");
            $result = $stmt->execute([$profile_image, $name, $gmail, $hashedPassword, $department, $user_role, $updated_at, $supervisor_id]);
        } elseif (!empty($password)) {
            // Update password only
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $connect->prepare("UPDATE immediate_supervisor SET name = ?, gmail = ?, password = ?, department = ?, user_role = ?, updated_at = ? WHERE supervisor_id = ?");
            $result = $stmt->execute([$name, $gmail, $hashedPassword, $department, $user_role, $updated_at, $supervisor_id]);
        } elseif ($updateProfileImage) {
            // Update profile image only
            $stmt = $connect->prepare("UPDATE immediate_supervisor SET profile_image = ?, name = ?, gmail = ?, department = ?, user_role = ?, updated_at = ? WHERE supervisor_id = ?");
            $result = $stmt->execute([$profile_image, $name, $gmail, $department, $user_role, $updated_at, $supervisor_id]);
        } else {
            // Don't update password or profile image
            $stmt = $connect->prepare("UPDATE immediate_supervisor SET name = ?, gmail = ?, department = ?, user_role = ?, updated_at = ? WHERE supervisor_id = ?");
            $result = $stmt->execute([$name, $gmail, $department, $user_role, $updated_at, $supervisor_id]);
        }

        if ($result) {
            echo json_encode(['status' => 'success', 'message' => 'Supervisor updated successfully']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update supervisor']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit();
}

// Delete Supervisor
if (isset($_POST['deleteSupervisor'])) {
    $supervisor_id = $_POST['supervisor_id'];

    try {
        $stmt = $connect->prepare("DELETE FROM immediate_supervisor WHERE supervisor_id = ?");
        $result = $stmt->execute([$supervisor_id]);

        if ($result) {
            echo json_encode(['status' => 'success', 'message' => 'Supervisor deleted successfully']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to delete supervisor']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit();
}

// Get Supervisor
if (isset($_POST['getSupervisor'])) {
    $supervisor_id = $_POST['supervisor_id'];

    try {
        $stmt = $connect->prepare("SELECT supervisor_id, profile_image, name, gmail, department, user_role, created_at, updated_at FROM immediate_supervisor WHERE supervisor_id = ?");
        $stmt->execute([$supervisor_id]);
        $supervisor = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($supervisor) {
            // Convert the profile image to base64 if it exists
            if (isset($supervisor['profile_image']) && $supervisor['profile_image']) {
                $supervisor['profile_image'] = base64_encode($supervisor['profile_image']);
            }

            echo json_encode(['status' => 'success', 'data' => $supervisor]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Supervisor not found']);
        }
    } catch (PDOException $e) {
        echo json_encode(['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit();
}

?>

<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5">
    <meta name="description" content="OJT Monitoring System is a platform designed to track and manage on-the-job training programs efficiently.">
    <meta name="author" content="Your Name or Organization">
    <meta name="theme-color" content="#FFFFFF">

    <link rel="icon" href="<?php echo $assets_path; ?>img/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="<?php echo $assets_path; ?>MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="<?php echo $assets_path; ?>boxicons-master/css/boxicons.min.css">

    <title>OJT Monitoring System</title>
    <link rel="stylesheet" href="main_style.css">

</head>

<body>
    <?php include 'sidebar.php' ?>

    <div class="main-content " id="main-content">
        <?php include 'navbar_header.php' ?>

        <div class="content " style="border-radius: 20px; margin-left: 10px; margin-right: 10px; ">

            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Manage Supervisor</li>
                </ol>
            </nav>

            <main>
                <?php
                $recordsPerPageOptions = [5, 10, 25, 50, 100, 250, 500];
                $defaultRecordsPerPage = 5;
                $recordsPerPage = isset($_GET['recordsPerPage']) && in_array($_GET['recordsPerPage'], $recordsPerPageOptions)
                    ? (int)$_GET['recordsPerPage']
                    : $defaultRecordsPerPage;

                $currentPage = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                $startFrom = ($currentPage - 1) * $recordsPerPage;

                // Get search term if provided
                $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';

                // Build the query based on search term
                $countQuery = "SELECT COUNT(*) as total FROM immediate_supervisor";
                $supervisorsQuery = "SELECT * FROM immediate_supervisor";

                // Add search condition if search term is provided
                if (!empty($searchTerm)) {
                    $searchCondition = " WHERE name LIKE :searchTerm OR gmail LIKE :searchTerm OR department LIKE :searchTerm";
                    $countQuery .= $searchCondition;
                    $supervisorsQuery .= $searchCondition;
                }

                // Add order and limit
                $supervisorsQuery .= " ORDER BY created_at DESC LIMIT :start, :recordsPerPage";

                // Get total supervisors count
                try {
                    $countStmt = $connect->prepare($countQuery);

                    if (!empty($searchTerm)) {
                        $searchParam = '%' . $searchTerm . '%';
                        $countStmt->bindParam(':searchTerm', $searchParam, PDO::PARAM_STR);
                    }

                    $countStmt->execute();
                    $totalSupervisors = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

                    // Calculate total pages
                    $totalPages = ceil($totalSupervisors / $recordsPerPage);

                    // Get supervisors for current page
                    $supervisorsStmt = $connect->prepare($supervisorsQuery);

                    if (!empty($searchTerm)) {
                        $searchParam = '%' . $searchTerm . '%';
                        $supervisorsStmt->bindParam(':searchTerm', $searchParam, PDO::PARAM_STR);
                    }

                    $supervisorsStmt->bindParam(':start', $startFrom, PDO::PARAM_INT);
                    $supervisorsStmt->bindParam(':recordsPerPage', $recordsPerPage, PDO::PARAM_INT);
                    $supervisorsStmt->execute();
                    $supervisors = $supervisorsStmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (PDOException $e) {
                    // Handle database error
                    $totalSupervisors = 0;
                    $totalPages = 1;
                    $supervisors = [];
                }
                ?>
                <style>
                    .table-responsive.card {
                        margin: 0;
                        padding: 0;
                    }

                    .table {
                        margin-bottom: 0;
                    }

                    tfoot th {
                        padding: 0.75rem;
                    }

                    /* Modern UI Styles for Rows Dropdown and Pagination */
                    .modern-dropdown {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .modern-dropdown .dropdown-label {
                        font-size: 14px;
                        font-weight: 500;
                        color: #555;
                    }

                    .modern-dropdown .btn-dropdown {
                        background: rgba(255, 255, 255, 0.8);
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.18);
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                        border-radius: 8px;
                        padding: 6px 12px;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        min-width: 80px;
                        transition: all 0.2s ease;
                    }

                    .modern-dropdown .btn-dropdown:hover {
                        background: rgba(255, 255, 255, 0.9);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
                    }

                    .modern-dropdown .dropdown-menu {
                        background: rgba(255, 255, 255, 0.9);
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.18);
                        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
                        border-radius: 8px;
                        overflow: hidden;
                        padding: 4px 0;
                    }

                    .modern-dropdown .dropdown-item {
                        padding: 6px 16px;
                        font-size: 14px;
                        color: #333;
                        transition: all 0.2s ease;
                    }

                    .modern-dropdown .dropdown-item:hover {
                        background: rgba(0, 123, 255, 0.1);
                    }

                    /* Modern Pagination */
                    .modern-pagination {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-top: 16px;
                        height: auto;
                        padding: 0 4px;
                    }

                    .modern-pagination .page-info {
                        font-size: 14px;
                        color: #555;
                        font-weight: 500;
                    }

                    .modern-pagination .nav-buttons {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .modern-pagination .nav-btn {
                        background: rgba(255, 255, 255, 0.8);
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.18);
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                        color: #555;
                        font-size: 14px;
                        padding: 6px 12px;
                        border-radius: 6px;
                        transition: all 0.2s ease;
                        height: 36px;
                        width: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                    }

                    .modern-pagination .nav-btn:hover {
                        background: rgba(255, 255, 255, 0.9);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
                        color: #333;
                    }

                    .modern-pagination .nav-btn.disabled {
                        background: rgba(255, 255, 255, 0.5);
                        color: #aaa;
                        cursor: not-allowed;
                        box-shadow: none;
                    }

                    /* Glassmorphism Modal Styles */
                    .modal-content {
                        background: rgba(255, 255, 255, 0.95);
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.18);
                        border-radius: 16px;
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                        max-width: 450px;
                        margin: 0 auto;
                    }

                    .modal-dialog.modal-md {
                        max-width: 450px;
                    }

                    .form-control-sm, .form-select-sm {
                        height: calc(1.5em + 0.5rem + 2px);
                        padding: 0.25rem 0.5rem;
                        font-size: 0.875rem;
                        border-radius: 0.2rem;
                    }

                    .form-outline {
                        margin-bottom: 10px;
                    }

                    .form-outline .form-control, .form-control, .form-select {
                        background-color: rgba(255, 255, 255, 0.7);
                        border: 1px solid rgba(0, 0, 0, 0.05);
                    }

                    .form-outline .form-control:focus, .form-control:focus, .form-select:focus {
                        background-color: rgba(255, 255, 255, 0.9);
                        box-shadow: 0 0 0 1px rgba(13, 110, 253, 0.25);
                    }

                    .form-label {
                        color: #555;
                        font-size: 0.85rem;
                    }

                    .modal-footer .btn {
                        border-radius: 6px;
                        padding: 0.25rem 0.75rem;
                        font-size: 0.875rem;
                        transition: all 0.2s ease;
                    }

                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                        .modern-dropdown .btn-dropdown,
                        .modern-pagination .nav-btn {
                            padding: 5px 10px;
                            font-size: 13px;
                            height: 34px;
                        }

                        .modern-pagination .page-info {
                            font-size: 13px;
                        }

                        .modal-dialog.modal-md {
                            max-width: 90%;
                            margin: 10px auto;
                        }

                        .modal-title {
                            font-size: 15px !important;
                        }

                        .modal-body {
                            padding: 12px !important;
                        }

                        .modal-footer {
                            padding: 8px 12px !important;
                        }
                    }

                    @media (max-width: 425px) {
                        .modern-dropdown .btn-dropdown,
                        .modern-pagination .nav-btn {
                            padding: 4px 8px;
                            font-size: 12px;
                            height: 32px;
                            width: 32px;
                        }

                        .modern-dropdown .dropdown-label,
                        .modern-pagination .page-info {
                            font-size: 12px;
                        }

                        .modal-dialog.modal-md {
                            max-width: 95%;
                            margin: 10px auto;
                        }

                        .modal-body {
                            padding: 10px !important;
                        }

                        .modal-header {
                            padding: 10px !important;
                        }

                        .modal-footer {
                            padding: 8px 10px !important;
                        }

                        .modal-footer .btn {
                            padding: 0.2rem 0.6rem;
                            font-size: 0.8rem;
                        }

                        .form-label {
                            font-size: 0.8rem;
                            margin-bottom: 0.2rem !important;
                        }
                    }
                </style>

                <div class="container-xxl flex-grow-1 container-p-y pt-4">
                    <section>
                        <div class="row">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mt-3 mb-3">
                                        <h2 style="font-size: 16px;">Supervisor Lists</h2>
                                        <div class="d-flex align-items-center col-md-6">
                                            <div class="modern-dropdown me-2">
                                                <span class="dropdown-label">Rows:</span>
                                                <div class="btn-group">
                                                    <button class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                        <?php echo $recordsPerPage; ?>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php foreach ($recordsPerPageOptions as $option): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="?recordsPerPage=<?php echo $option; ?>&page=1">
                                                                    <?php echo $option; ?>
                                                                </a>
                                                            </li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </div>
                                            </div>
                                            <form id="searchForm" method="GET" class="d-flex">
                                                <input type="hidden" name="recordsPerPage" value="<?php echo $recordsPerPage; ?>">
                                                <input type="hidden" name="page" value="1">
                                                <input type="text" id="searchInput" name="search" class="form-control form-control-md me-1" placeholder="Search..." value="<?php echo htmlspecialchars($searchTerm); ?>">
                                            </form>
                                            <button type="button" class="btn btn-md btn-primary col-md-4" style="text-transform: capitalize; font-size: 12px;" data-mdb-modal-init data-mdb-target="#addSupervisorModal"><i class="fa fa-plus"></i> New Supervisor</button>
                                        </div>
                                    </div>

                                    <style>
                                        /* Column width styles */
                                        .table th:nth-child(1), .table td:nth-child(1) { width: 5%; } /* # */
                                        .table th:nth-child(2), .table td:nth-child(2) { width: 8%; } /* Profile */
                                        .table th:nth-child(3), .table td:nth-child(3) { width: 25%; } /* Name */
                                        .table th:nth-child(4), .table td:nth-child(4) { width: 20%; } /* Gmail */
                                        .table th:nth-child(5), .table td:nth-child(5) {
                                            width: 12%;
                                            max-width: 120px;
                                            white-space: normal;
                                            word-wrap: break-word;
                                        } /* Department */
                                        .table th:nth-child(6), .table td:nth-child(6) { width: 15%; } /* Created At */
                                        .table th:nth-child(7), .table td:nth-child(7) { width: 15%; } /* Actions */
                                    </style>
                                    <div class="table-responsive card">
                                        <table class="table table-hover table-bordered table-sm">
                                            <thead>
                                                <tr>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">#</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Profile</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Name</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Gmail</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Department</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Created At</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="supervisorTableBody">
                                                <?php if (empty($supervisors)): ?>
                                                    <tr>
                                                        <td colspan="7" class="text-center">No data found</td>
                                                    </tr>
                                                <?php else: ?>
                                                    <?php
                                                    $counter = ($currentPage - 1) * $recordsPerPage + 1; // Start counter from the current page's first record
                                                    foreach ($supervisors as $supervisor):
                                                        // Mask email for display
                                                        $email = $supervisor['gmail'];
                                                        $atPos = strpos($email, '@');
                                                        if ($atPos !== false) {
                                                            $username = substr($email, 0, $atPos);
                                                            $domain = substr($email, $atPos);
                                                            $maskedUsername = substr($username, 0, 1) . str_repeat('*', strlen($username) - 1);
                                                            $maskedEmail = $maskedUsername . $domain;
                                                        } else {
                                                            $maskedEmail = $email;
                                                        }

                                                        // Format date
                                                        $createdDate = new DateTime($supervisor['created_at']);
                                                        $formattedDate = $createdDate->format('F j, Y');
                                                    ?>
                                                        <tr>
                                                            <td class="text-center"><?php echo $counter++; ?></td>
                                                            <td class="text-center">
                                                                <?php if (!empty($supervisor['profile_image'])): ?>
                                                                    <img src="data:image/jpeg;base64,<?php echo base64_encode($supervisor['profile_image']); ?>" alt="Profile" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                                <?php else: ?>
                                                                    <img src="<?php echo $default_profile_img; ?>" alt="Default Profile" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                                                <?php endif; ?>
                                                            </td>
                                                            <td class="text-center"><?php echo htmlspecialchars($supervisor['name']); ?></td>
                                                            <td class="text-center"><?php echo htmlspecialchars($maskedEmail); ?></td>
                                                            <td class="text-center" style="white-space: normal; word-wrap: break-word;">
                                                                <?php
                                                                $dept = $supervisor['department'];
                                                                // Format department names for better display
                                                                if ($dept == "ILCDB - ICT Literacy and Competency Development Bureau") {
                                                                    echo "ILCDB";
                                                                } elseif ($dept == "eLGU - Electronic Local Government Unit") {
                                                                    echo "eLGU";
                                                                } else {
                                                                    echo htmlspecialchars($dept);
                                                                }
                                                                ?>
                                                            </td>
                                                            <td class="text-center"><?php echo $formattedDate; ?></td>
                                                            <td class="text-center">
                                                                <button type="button" class="btn btn-outline-primary btn-floating btn-edit" data-mdb-target="#editSupervisorModal" data-mdb-ripple-init data-id="<?php echo $supervisor['supervisor_id']; ?>">
                                                                    <i class="bx bx-edit" style="font-size: 15px;"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-outline-danger btn-floating btn-delete" data-mdb-ripple-init data-id="<?php echo $supervisor['supervisor_id']; ?>">
                                                                    <i class="bx bx-trash" style="font-size: 15px;"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">#</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Profile</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Name</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Gmail</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Department</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Created At</th>
                                                    <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Actions</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>

                                    <!-- Modern Pagination Controls -->
                                    <div class="modern-pagination">
                                        <!-- Page Information (Left) -->
                                        <div class="page-info">
                                            <?php
                                            // Calculate the range of rows being displayed
                                            $startRow = ($currentPage - 1) * $recordsPerPage + 1;
                                            $endRow = min($startRow + $recordsPerPage - 1, $totalSupervisors);

                                            // Handle case when there are no records
                                            if ($totalSupervisors == 0) {
                                                $startRow = 0;
                                                $endRow = 0;
                                            }
                                            ?>
                                            Results: <?php echo $startRow; ?>-<?php echo $endRow; ?> of <?php echo $totalSupervisors; ?>
                                        </div>

                                        <!-- Navigation Buttons (Right) -->
                                        <div class="nav-buttons">
                                            <!-- Previous Button -->
                                            <a class="nav-btn <?php echo $currentPage == 1 ? 'disabled' : ''; ?>"
                                                href="<?php echo $currentPage > 1 ? '?page=' . ($currentPage - 1) . '&recordsPerPage=' . $recordsPerPage . (!empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '') : 'javascript:void(0)'; ?>"
                                                aria-label="Previous"
                                                <?php echo $currentPage == 1 ? 'onclick="return false;"' : ''; ?>>
                                                <i class="fa fa-chevron-left"></i>
                                            </a>

                                            <!-- Next Button -->
                                            <a class="nav-btn <?php echo $currentPage == $totalPages || $totalPages == 0 ? 'disabled' : ''; ?>"
                                                href="<?php echo $currentPage < $totalPages ? '?page=' . ($currentPage + 1) . '&recordsPerPage=' . $recordsPerPage . (!empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '') : 'javascript:void(0)'; ?>"
                                                aria-label="Next"
                                                <?php echo $currentPage == $totalPages || $totalPages == 0 ? 'onclick="return false;"' : ''; ?>>
                                                <i class="fa fa-chevron-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>

        <!-- Copyright -->
        <footer class="text-center p-2 mt-5" style="background-color: #F5F5F9;">
            © 2025 Developed by:
            <a class="text-dark" href="#">John Lloyd Caban</a>
        </footer>
        <!-- Copyright -->
    </div>

    <!-- ADD MODAL -->
    <div class="modal fade" id="addSupervisorModal" tabindex="-1" aria-labelledby="addSupervisorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content" style="border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                <div class="modal-header bg-light" style="border-bottom: 1px solid rgba(0,0,0,0.05); padding: 12px 15px;">
                    <h5 class="modal-title" id="addSupervisorModalLabel" style="font-weight: 600; color: #333; font-size: 16px;">Add New Supervisor</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 15px;">
                    <form id="addSupervisorForm" enctype="multipart/form-data">
                        <div class="row mb-3">
                            <div class="col-md-12 text-center">
                                <div class="mb-2">
                                    <img id="profile_preview" src="<?php echo $default_profile_img; ?>" alt="Profile Preview" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 2px solid #e0e0e0;">
                                </div>
                                <div class="mb-2">
                                    <label for="profile_image" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-upload me-1"></i> Upload Photo
                                    </label>
                                    <input type="file" class="d-none" id="profile_image" name="profile_image" accept="image/*">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Full Name</label>
                                <input type="text" class="form-control form-control-sm" id="name" name="name" required>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Gmail</label>
                                <input type="email" class="form-control form-control-sm" id="gmail" name="gmail" required>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Password</label>
                                <input type="password" class="form-control form-control-sm" id="password" name="password">
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Department</label>
                                <select class="form-select form-select-sm" id="department" name="department" required>
                                    <option value="Cybersecurity Bureau">Cybersecurity Bureau</option>
                                    <option value="ILCDB - ICT Literacy and Competency Development Bureau">ILCDB - ICT Literacy & Competency Dev.</option>
                                    <option value="FreeWifi4All">FreeWifi4All</option>
                                    <option value="eLGU - Electronic Local Government Unit">eLGU - Electronic Local Gov. Unit</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">User Role</label>
                                <select class="form-select form-select-sm" id="user_role" name="user_role" required>
                                    <option value="Admin">Admin</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="padding: 10px 15px;">
                    <button type="button" class="btn btn-sm btn-secondary" data-mdb-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary" id="addSupervisorBtn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- EDIT MODAL -->
    <div class="modal fade" id="editSupervisorModal" tabindex="-1" aria-labelledby="editSupervisorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content" style="border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                <div class="modal-header bg-light" style="border-bottom: 1px solid rgba(0,0,0,0.05); padding: 12px 15px;">
                    <h5 class="modal-title" id="editSupervisorModalLabel" style="font-weight: 600; color: #333; font-size: 16px;">Edit Supervisor</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 15px;">
                    <form id="editSupervisorForm" enctype="multipart/form-data">
                        <input type="hidden" id="edit_supervisor_id" name="supervisor_id">

                        <div class="row mb-3">
                            <div class="col-md-12 text-center">
                                <div class="mb-2">
                                    <img id="edit_profile_preview" src="<?php echo $default_profile_img; ?>" alt="Profile Preview" class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 2px solid #e0e0e0;">
                                </div>
                                <div class="mb-2">
                                    <label for="edit_profile_image" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-upload me-1"></i> Change Photo
                                    </label>
                                    <input type="file" class="d-none" id="edit_profile_image" name="profile_image" accept="image/*">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Full Name</label>
                                <input type="text" class="form-control form-control-sm" id="edit_name" name="name" required>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Gmail</label>
                                <input type="email" class="form-control form-control-sm" id="edit_gmail" name="gmail" required>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Password</label>
                                <input type="password" class="form-control form-control-sm" id="edit_password" name="password" placeholder="Leave blank to keep current password">
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">Department</label>
                                <select class="form-select form-select-sm" id="edit_department" name="department" required>
                                    <option value="Cybersecurity Bureau">Cybersecurity Bureau</option>
                                    <option value="ILCDB - ICT Literacy and Competency Development Bureau">ILCDB - ICT Literacy & Competency Dev.</option>
                                    <option value="FreeWifi4All">FreeWifi4All</option>
                                    <option value="eLGU - Electronic Local Government Unit">eLGU - Electronic Local Gov. Unit</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label class="form-label small text-muted mb-1">User Role</label>
                                <select class="form-select form-select-sm" id="edit_user_role" name="user_role" required>
                                    <option value="Admin">Admin</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="padding: 10px 15px;">
                    <button type="button" class="btn btn-sm btn-secondary" data-mdb-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-sm btn-primary" id="updateSupervisorBtn">Update</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script type="text/javascript" src="<?php echo $assets_path; ?>MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Toggle sidebar functionality
            const toggleSidebarButton = document.getElementById('toggle-sidebar');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const headerLogo = document.getElementById('header-logo');

            if (toggleSidebarButton) {
                toggleSidebarButton.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    mainContent.classList.toggle('active');
                    if (headerLogo) {
                        headerLogo.classList.toggle('hide-logo');
                    }
                });
            }

            // Image preview for add form
            const profileImageInput = document.getElementById('profile_image');
            const profilePreview = document.getElementById('profile_preview');

            if (profileImageInput && profilePreview) {
                profileImageInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profilePreview.src = e.target.result;
                        };
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }

            // Image preview for edit form
            const editProfileImageInput = document.getElementById('edit_profile_image');
            const editProfilePreview = document.getElementById('edit_profile_preview');

            if (editProfileImageInput && editProfilePreview) {
                editProfileImageInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            editProfilePreview.src = e.target.result;
                        };
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }

            // Search functionality
            const searchInput = document.getElementById('searchInput');
            const searchForm = document.getElementById('searchForm');
            if (searchInput) {
                searchInput.addEventListener('keyup', function(e) {
                    // Submit form on Enter key
                    if (e.key === 'Enter') {
                        searchForm.submit();
                    }
                });
            }

            // Add Supervisor Form Submission
            const addSupervisorBtn = document.getElementById('addSupervisorBtn');
            if (addSupervisorBtn) {
                addSupervisorBtn.addEventListener('click', function() {
                    const form = document.getElementById('addSupervisorForm');
                    const formData = new FormData(form);
                    formData.append('addSupervisor', true);

                    fetch('manage_supervisor.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            Swal.fire({
                                title: 'Success!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                // Close modal
                                const modal = document.getElementById('addSupervisorModal');
                                const modalInstance = mdb.Modal.getInstance(modal);
                                modalInstance.hide();

                                // Reset form
                                form.reset();

                                // Reload page to show updated data
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            title: 'Error!',
                            text: 'An unexpected error occurred',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    });
                });
            }

            // Update Supervisor Form Submission
            const updateSupervisorBtn = document.getElementById('updateSupervisorBtn');
            if (updateSupervisorBtn) {
                updateSupervisorBtn.addEventListener('click', function() {
                    const form = document.getElementById('editSupervisorForm');
                    const formData = new FormData(form);
                    formData.append('updateSupervisor', true);

                    fetch('manage_supervisor.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            Swal.fire({
                                title: 'Success!',
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                // Close modal
                                const modal = document.getElementById('editSupervisorModal');
                                const modalInstance = mdb.Modal.getInstance(modal);
                                modalInstance.hide();

                                // Reload page to show updated data
                                window.location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire({
                            title: 'Error!',
                            text: 'An unexpected error occurred',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    });
                });
            }

            // Delete Supervisor Button Click
            document.querySelectorAll('.btn-delete').forEach(button => {
                button.addEventListener('click', function() {
                    const supervisorId = this.getAttribute('data-id');

                    // Show confirmation dialog
                    Swal.fire({
                        title: 'Are you sure?',
                        text: "You won't be able to revert this!",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Send delete request
                            const formData = new FormData();
                            formData.append('deleteSupervisor', true);
                            formData.append('supervisor_id', supervisorId);

                            fetch('manage_supervisor.php', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.status === 'success') {
                                    Swal.fire(
                                        'Deleted!',
                                        data.message,
                                        'success'
                                    ).then(() => {
                                        // Reload page to show updated data
                                        window.location.reload();
                                    });
                                } else {
                                    Swal.fire(
                                        'Error!',
                                        data.message,
                                        'error'
                                    );
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                Swal.fire(
                                    'Error!',
                                    'An unexpected error occurred',
                                    'error'
                                );
                            });
                        }
                    });
                });
            });

            // Edit Supervisor Button Click
            document.querySelectorAll('.btn-edit').forEach(button => {
                button.addEventListener('click', function() {
                    const supervisorId = this.getAttribute('data-id');

                    // Fetch supervisor data
                    const formData = new FormData();
                    formData.append('getSupervisor', true);
                    formData.append('supervisor_id', supervisorId);

                    fetch('manage_supervisor.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Populate form with supervisor data
                            document.getElementById('edit_supervisor_id').value = data.data.supervisor_id;
                            document.getElementById('edit_name').value = data.data.name;
                            document.getElementById('edit_gmail').value = data.data.gmail;

                            // Set the department value directly
                            document.getElementById('edit_department').value = data.data.department;

                            // Set the user role value
                            if (data.data.user_role) {
                                document.getElementById('edit_user_role').value = data.data.user_role;
                            } else {
                                // Default to Admin if not set
                                document.getElementById('edit_user_role').value = 'Admin';
                            }

                            // Update profile image if available
                            if (data.data.profile_image) {
                                document.getElementById('edit_profile_preview').src = 'data:image/jpeg;base64,' + data.data.profile_image;
                            } else {
                                document.getElementById('edit_profile_preview').src = '<?php echo $default_profile_img; ?>';
                            }

                            // Open the modal
                            const modal = document.getElementById('editSupervisorModal');
                            const modalInstance = new mdb.Modal(modal);
                            modalInstance.show();
                        } else {
                            Swal.fire(
                                'Error!',
                                data.message,
                                'error'
                            );
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        Swal.fire(
                            'Error!',
                            'An unexpected error occurred',
                            'error'
                        );
                    });
                });
            });

            // Initialize MDB components
            document.querySelectorAll('[data-mdb-dropdown-init]').forEach(element => {
                new mdb.Dropdown(element);
            });

            document.querySelectorAll('[data-mdb-modal-init]').forEach(element => {
                new mdb.Modal(element);
            });
        });
    </script>
</body>
</html>
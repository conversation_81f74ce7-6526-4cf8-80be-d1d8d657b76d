
<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="OJT Monitoring System is a platform designed to track and manage on-the-job training programs efficiently.">
    <meta name="author" content="Your Name or Organization">

    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="../assets/boxicons-master/css/boxicons.min.css">
    <script src="../assets/js/JsBarcode.all.min.js"></script>

    <title>OJT Monitoring System</title>

    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        body {
            display: flex;
            flex-direction: column;
            background-image: url('../assets/img/landing_page.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            zoom: 70%;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            position: relative;
        }

        .glassmorphism-form {
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            width: 500px;
            max-width: 90%;
            height: auto;
            position: absolute;
            left: 200px;
            bottom: 30px;
        }

        .glassmorphism-form .form-control {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 2px solid rgba(183, 183, 183, 0.8);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: #000;
        }

        .glassmorphism-form .btn {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: #000;
            cursor: pointer;
        }

        #reader {
            width: 100%;
        }

        footer {
            width: 100%;
            margin: 0;
            padding: 10px 0;
            background-color: #F5F5F9;
            text-align: center;
            position: relative;
        }

        .footer-date-time {
            font-weight: bold;
        }
    </style>
</head>

<body>
    <script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
    <div class="content-wrapper">
        <section>
            <form class="glassmorphism-form" action="landing_page.php" method="POST">
                <h2 class="text-center py-2" style="color: #000;"><b>Monitoring System</b></h2>
                <label class="mt-2" for="employeeId" style="color: #000;">Employee ID</label>
                <input type="text" id="employeeId" name="employeeId" class="form-control mb-3" placeholder="Enter Employee ID" required>
                <label class="mt-2" for="action" style="color: #000;">Action</label>
                <select id="action" name="action" class="form-control mb-3" required>
                    <option value="time_in">Time In</option>
                    <option value="time_out">Time Out</option>
                </select>
                <label class="mt-2" for="session" style="color: #000;">Session</label>
                <select id="session" name="session" class="form-control mb-3" required>
                    <option value="morning">Morning</option>
                    <option value="afternoon">Afternoon</option>
                </select>
                <div style="text-align: center ">
                    <div id="reader"></div>
                    <div id="result"></div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Submit</button>
            </form>
        </section>
    </div>

    <!-- Footer with Date and Time -->
    <footer class="text-center p-2">
        <div class="footer-date-time" id="dateTime">Loading...</div>
        © 2025 Developed by:
        <a class="text-dark" href="#">John Lloyd Caban</a>
    </footer>
    <!-- Footer -->

    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Function to update the date and time in GMT+8
            function updateDateTime() {
                const now = new Date();

                // Adjust the time to GMT+8
                const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
                const gmt8Offset = 8 * 3600000; // GMT+8 offset in milliseconds
                const gmt8Time = new Date(utc + gmt8Offset);

                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                };

                document.getElementById('dateTime').innerText = gmt8Time.toLocaleString('en-US', options);
            }

            // Update the date and time every second
            setInterval(updateDateTime, 1000);
            updateDateTime(); // Initial call to display immediately
        });

        const scanner = new Html5QrcodeScanner("reader", {
            qrbox: {
                width: 250,
                height: 250,
            },
            fps: 20,
        });

        scanner.render(success, error);

        function success(result) {
            document.getElementById("result").innerHTML = `
            <h2>Success!</h2>
            `;
            document.getElementById("employeeId").value = result; // Set scanned value to input field
            scanner.clear();
            document.getElementById("reader").remove();
        }

        function error(err) {
            console.error(err);
        }
    </script>
</body>

</html>
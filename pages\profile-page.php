<?php
// profile-page.php - Enhanced user profile page with update functionality

// Start the session if it hasn't been started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not logged in
    header("Location: login.php");
    exit;
}

// Include database connection and helpers
include 'database/db_connection.php';
include '../includes/email_helper.php';

// Initialize variables
$success_message = '';
$error_message = '';
$user_id = $_SESSION['user_id'];
$active_tab = isset($_POST['active_tab']) ? $_POST['active_tab'] : 'edit-profile';

// Fetch user data from database based on user_type
try {
    // Check if user_type is set in session
    $user_type = $_SESSION['user_type'] ?? 'employee';

    if ($user_type === 'supervisor') {
        // Fetch from immediate_supervisor table for Admin users
        $stmt = $connect->prepare("SELECT * FROM immediate_supervisor WHERE supervisor_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        // Fetch from tbl_employee table for Employee users
        $stmt = $connect->prepare("SELECT * FROM tbl_employee WHERE tbl_emp_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    if (!$user) {
        $error_message = "User not found.";
    }
} catch (PDOException $e) {
    $error_message = "Database error: " . $e->getMessage();
}

// Handle form submission for profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // Check if user exists before processing form
    if (!$user) {
        $error_message = "Cannot update profile: User not found.";
    } else {
        try {
            // Get form data
            $mobile_number = $_POST['mobile_number'] ?? ($user['mobile_number'] ?? '');
            // Ensure mobile number starts with +63
            if (!empty($mobile_number) && strpos($mobile_number, '+63') !== 0) {
                $mobile_number = '+63' . ltrim($mobile_number, '+63');
            }

            // Only get gender for employee users, not for supervisors
            $gender = '';
            if ($user_type === 'employee') {
                $gender = $_POST['gender'] ?? ($user['gender'] ?? '');
            }
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';

            // Check if password change is requested
            $update_password = !empty($current_password) && !empty($new_password) && !empty($confirm_password);

            // Validate password if update is requested
            if ($update_password) {
                // Verify current password
                if (!password_verify($current_password, $user['password'])) {
                    $error_message = "Current password is incorrect.";
                }
                // Check if new passwords match
                else if ($new_password !== $confirm_password) {
                    $error_message = "New passwords do not match.";
                }
                // Check password strength
                else if (strlen($new_password) < 8) {
                    $error_message = "New password must be at least 8 characters long.";
                }
            }

            // Process profile image if uploaded
            $profile_image = null;
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
                // Check file type
                $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                $file_type = $_FILES['profile_image']['type'];

                if (!in_array($file_type, $allowed_types)) {
                    $error_message = "Only JPG, PNG, and GIF images are allowed.";
                } else {
                    // Read file content
                    $profile_image = file_get_contents($_FILES['profile_image']['tmp_name']);
                }
            }

            // If no errors, update profile
            if (empty($error_message)) {
                // Determine which table to update based on user_type
                $user_type = $_SESSION['user_type'] ?? 'employee';
                $table = ($user_type === 'supervisor') ? 'immediate_supervisor' : 'tbl_employee';
                $id_field = ($user_type === 'supervisor') ? 'supervisor_id' : 'tbl_emp_id';

                // For supervisors, we need to handle the SQL differently since the table structure is different
                if ($user_type === 'supervisor') {
                    // Build the SQL parts based on what's being updated
                    $sql_parts = [];
                    $params = [];

                    // Include name if it's provided
                    if (isset($_POST['name'])) {
                        $sql_parts[] = "name = ?";
                        $params[] = $_POST['name'];
                    }

                    // Include department if it's provided
                    if (isset($_POST['department'])) {
                        $sql_parts[] = "department = ?";
                        $params[] = $_POST['department'];
                    }

                    // Include gmail if it's provided
                    if (isset($_POST['gmail'])) {
                        $sql_parts[] = "gmail = ?";
                        $params[] = $_POST['gmail'];
                    }

                    // We don't include mobile_number for supervisors since it's not in the table schema

                    // We don't include gender for supervisors since it's not in the table schema

                    // Add password if updating
                    if ($update_password) {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $sql_parts[] = "password = ?";
                        $params[] = $hashed_password;
                    }

                    // Add profile image if updating
                    if ($profile_image !== null) {
                        $sql_parts[] = "profile_image = ?";
                        $params[] = $profile_image;

                        // Update session profile image
                        $_SESSION['profile_image'] = base64_encode($profile_image);
                    }

                    // Always update the updated_at timestamp
                    $sql_parts[] = "updated_at = NOW()";

                    // Only proceed if we have something to update
                    if (!empty($sql_parts)) {
                        $sql = "UPDATE $table SET " . implode(", ", $sql_parts) . " WHERE $id_field = ?";
                        $params[] = $user_id;

                        $stmt = $connect->prepare($sql);
                        $stmt->execute($params);
                    }
                } else {
                    // For employees, use the original update logic
                    if ($update_password && $profile_image !== null) {
                        // Update password and profile image
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $connect->prepare("UPDATE $table SET mobile_number = ?, gender = ?, password = ?, profile_image = ?, updated_at = NOW() WHERE $id_field = ?");
                        $stmt->execute([$mobile_number, $gender, $hashed_password, $profile_image, $user_id]);
                    } else if ($update_password) {
                        // Update password only
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $connect->prepare("UPDATE $table SET mobile_number = ?, gender = ?, password = ?, updated_at = NOW() WHERE $id_field = ?");
                        $stmt->execute([$mobile_number, $gender, $hashed_password, $user_id]);
                    } else if ($profile_image !== null) {
                        // Update profile image only
                        $stmt = $connect->prepare("UPDATE $table SET mobile_number = ?, gender = ?, profile_image = ?, updated_at = NOW() WHERE $id_field = ?");
                        $stmt->execute([$mobile_number, $gender, $profile_image, $user_id]);

                        // Update session profile image
                        $_SESSION['profile_image'] = base64_encode($profile_image);
                    } else {
                        // Update basic info only
                        $stmt = $connect->prepare("UPDATE $table SET mobile_number = ?, gender = ?, updated_at = NOW() WHERE $id_field = ?");
                        $stmt->execute([$mobile_number, $gender, $user_id]);
                    }
                }

                $success_message = "Profile updated successfully!";

                // Refresh user data
                $stmt = $connect->prepare("SELECT * FROM $table WHERE $id_field = ?");
                $stmt->execute([$user_id]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
            }
        } catch (PDOException $e) {
            $error_message = "Update failed: " . $e->getMessage();
        }
    }
}

// Initialize default values
$date_registered = 'N/A';
$date_started = 'N/A';
$full_name = 'User Not Found';
$profile_image = null;
$user_type = $_SESSION['user_type'] ?? 'employee';

// Only process user data if the user was found
if ($user) {
    // Format dates for display (only for employees)
    if ($user_type === 'employee') {
        $date_registered = (isset($user['date_registered']) && $user['date_registered'] !== null) ? date('F j, Y', strtotime($user['date_registered'])) : 'N/A';
        $date_started = (isset($user['date_started']) && $user['date_started'] !== null) ? date('F j, Y', strtotime($user['date_started'])) : 'N/A';
    }

    // Get full name based on user type
    if ($user_type === 'supervisor') {
        // For supervisors, name is stored in a single field
        $full_name = $user['name'] ?? 'Supervisor';
    } else {
        // For employees, name is stored in separate fields
        $full_name = $user['first_name'] . ' ' . (isset($user['middle_initial']) && $user['middle_initial'] ? $user['middle_initial'] . '. ' : '') . $user['surname'];
    }

    // Get profile image
    $profile_image = !empty($user['profile_image']) ? base64_encode($user['profile_image']) : null;
}
?>

<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="OJT Monitoring System - Employee Profile with Minimalist Design">
    <meta name="author" content="DICT Surigao del Norte">

    <link rel="icon" href="../assets/img/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />

    <title>My Profile - OJT Monitoring System</title>

    <style>
        /* --- Define Minimalist Color Palette --- */
        :root {
            --minimalist-text-dark: #212529;
            --minimalist-text-muted: #6c757d;
            --minimalist-border-light: #dee2e6;
            --minimalist-border-lighter: #f1f1f1;
            --minimalist-bg-subtle: #f8f9fa;
            --minimalist-bg-main: #ffffff;
            --minimalist-primary: #3b71ca;
            --minimalist-primary-hover: #2f5aa9;
        }

        /* --- Global --- */
        body {
            background-color: #ffffff;
            color: var(--minimalist-text-dark);
            font-family: 'Roboto', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        main {
            flex: 1;
        }

        /* --- Header Styles --- */
        .page-header {
            background-color: rgba(243, 243, 243, 0.66);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .logo-container {
            width: 70px;
            height: 70px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
            border-radius: 50%;
            padding: 5px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .logo-container:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        }

        .logo-image {
            width: 100%;
            height: auto;
            max-width: 60px;
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .header-address {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0;
        }

        .return-button {
            display: inline-flex;
            align-items: center;
            background: #4285f4;
            color: white;
            padding: 8px 16px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(66, 133, 244, 0.3);
        }

        .return-button:hover {
            background: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(66, 133, 244, 0.4);
            color: white;
        }

        .return-icon {
            margin-right: 8px;
            display: flex;
            align-items: center;
        }

        .return-text {
            font-size: 0.9rem;
        }

        /* --- Profile Card Styles --- */
        .profile-card {
            border: 1px solid var(--minimalist-border-lighter);
            background-color: var(--minimalist-bg-main);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            border-radius: 0.5rem;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .profile-header {
            background-color: rgba(243, 243, 243, 0.66);
            padding: 1.5rem;
            border-bottom: 1px solid var(--minimalist-border-light);
        }

        .profile-image-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 0 auto;
        }

        .profile-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .profile-image-edit {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background-color: var(--minimalist-primary);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .profile-image-edit:hover {
            background-color: var(--minimalist-primary-hover);
        }

        .profile-tabs {
            border-bottom: 1px solid var(--minimalist-border-light);
            padding: 0 1rem;
            background-color: rgba(243, 243, 243, 0.66);
        }

        .profile-tab {
            padding: 1rem;
            font-weight: 500;
            color: var(--minimalist-text-muted);
            cursor: pointer;
            border: none;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            position: relative;
            z-index: 1;
            background: transparent;
            min-width: 120px;
            text-align: center;
            font-size: 1rem;
            font-family: 'Roboto', sans-serif;
        }

        .profile-tab.active {
            color: var(--minimalist-primary);
            border-bottom-color: var(--minimalist-primary);
            font-weight: 600;
        }

        .profile-tab:hover:not(.active) {
            color: var(--minimalist-text-dark);
            border-bottom-color: var(--minimalist-border-light);
            background-color: rgba(0, 0, 0, 0.02);
        }

        .profile-tab:focus {
            outline: none;
            box-shadow: none;
        }

        /* Ensure tab content is properly styled */
        .tab-content {
            position: relative;
            z-index: 0;
            width: 100%;
        }

        .profile-content {
            padding: 1.5rem;
        }

        .profile-section {
            margin-bottom: 2rem;
        }

        .profile-section-title {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--minimalist-border-light);
        }

        .profile-details dt {
            font-weight: 500;
            color: var(--minimalist-text-muted);
            margin-bottom: 0.5rem;
        }

        .profile-details dd {
            margin-bottom: 1rem;
        }

        /* --- Footer Styles --- */
        .modern-footer {
            background-color: rgba(243, 243, 243, 0.66);
            padding: 30px 0;
            margin-top: 50px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .footer-content {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
        }

        .footer-logo {
            margin-bottom: 15px;
        }

        .footer-logo-img {
            width: 50px;
            height: 50px;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .footer-logo-img:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .footer-info {
            color: #666;
        }

        .copyright {
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .developer {
            font-size: 0.85rem;
            margin-bottom: 0;
        }

        .developer-link {
            color: #4285f4;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .developer-link:hover {
            color: #34a853;
            text-decoration: underline;
        }

        /* --- Form Styles --- */
        .form-outline {
            margin-bottom: 1.5rem;
        }

        /* Mobile number input styles */
        .input-group .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
            font-weight: 500;
            color: #212529;
        }

        .input-group .form-control {
            border-left: none;
        }

        .password-toggle {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--minimalist-text-muted);
            z-index: 5;
        }

        /* Fix for form field labels */
        .form-outline .form-label {
            z-index: 5 !important;
            background-color: white;
            padding: 0 5px;
        }

        /* Fix for active form labels to ensure they don't overlap with content */
        .form-outline .form-control:not(:placeholder-shown) ~ .form-label,
        .form-outline .form-control.active ~ .form-label,
        .form-outline .form-control:focus ~ .form-label {
            transform: translateY(-1rem) translateY(0.1rem) scale(0.8);
            background-color: white;
            padding: 0 5px;
        }

        /* Ensure form inputs have proper placeholder behavior */
        .form-outline input.form-control {
            box-shadow: none !important;
        }

        /* Fix for form-outline animation */
        .form-outline .form-control:focus {
            border-color: #3b71ca;
            box-shadow: inset 0 0 0 1px #3b71ca !important;
        }

        /* Ensure password fields have proper stacking context */
        #change-password-content .form-outline {
            position: relative;
            z-index: 1;
        }

        /* --- Responsive adjustments --- */
        @media (max-width: 767.98px) {
            .profile-image {
                width: 120px;
                height: 120px;
            }

            .profile-image-container {
                width: 120px;
                height: 120px;
            }

            .profile-tab {
                padding: 0.75rem 0.5rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>

<body>
    <main>
        <div class="page-header">
            <div class="container py-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="logo-container">
                            <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="img-fluid logo-image">
                        </div>
                    </div>
                    <div class="col">
                        <div class="header-info">
                            <h3 class="header-title">DICT Surigao del Norte Provincial Office</h3>
                            <p class="header-address">Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p>
                        </div>
                    </div>
                    <div class="col-auto">
                        <?php
                        // Set default back URL based on user type
                        if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'supervisor') {
                            $back_url = 'dashboard.php'; // Default for supervisors/admins
                        } elseif (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'employee') {
                            $back_url = 'employee_dashboard.php'; // Default for employees
                        } else {
                            $back_url = 'dashboard.php'; // Default fallback
                        }

                        // Use JavaScript history.back() instead of a fixed URL
                        // This will use the browser's history to go back to the previous page
                        $use_history = true;

                        // However, if we came directly to this page (no referrer), use the default URL
                        if (!isset($_SERVER['HTTP_REFERER'])) {
                            $use_history = false;
                        } else {
                            // Check if referrer is from the same domain
                            $referer = $_SERVER['HTTP_REFERER'];
                            $host = $_SERVER['HTTP_HOST'];

                            // If referrer is not from the same domain, use the default URL
                            if (strpos($referer, $host) === false) {
                                $use_history = false;
                            }
                        }
                        ?>
                        <?php if ($use_history): ?>
                        <a href="javascript:history.back();" class="return-button">
                            <span class="return-icon"><i class="fa fa-arrow-left"></i></span>
                            <span class="return-text">Back</span>
                        </a>
                        <?php else: ?>
                        <a href="<?php echo htmlspecialchars($back_url); ?>" class="return-button">
                            <span class="return-icon"><i class="fa fa-arrow-left"></i></span>
                            <span class="return-text">Back</span>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="container flex-grow-1 pt-4">

            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="profile-card">
                        <div class="profile-header text-center">
                            <div class="profile-image-container mb-3">
                                <?php if ($profile_image): ?>
                                    <img src="data:image/jpeg;base64,<?php echo $profile_image; ?>" alt="Profile Image" class="profile-image rounded-circle">
                                <?php else: ?>
                                    <img src="../assets/img/boy.png" alt="Default Profile Image" class="profile-image rounded-circle">
                                <?php endif; ?>
                                <label for="profile_image_upload" class="profile-image-edit" title="Change profile picture">
                                    <i class="fas fa-camera"></i>
                                </label>
                            </div>
                            <h4 class="mb-1"><?php echo htmlspecialchars($full_name); ?></h4>
                            <?php if (isset($user['user_role']) && $user['user_role'] === 'Employee' && isset($user['employee_type'])): ?>
                                <p class="text-muted mb-2"><?php echo htmlspecialchars($user['employee_type']); ?></p>
                            <?php endif; ?>
                            <p class="badge bg-primary"><?php echo htmlspecialchars($user['user_role'] ?? 'User'); ?></p>
                        </div>
                        <div class="profile-content">
                            <div class="profile-section">
                                <h5 class="profile-section-title">Contact Information</h5>
                                <dl class="profile-details">
                                    <dt><i class="fas fa-envelope me-2"></i> Email</dt>
                                    <dd><?php echo htmlspecialchars(blurEmail($user['gmail'] ?? '')); ?></dd>

                                    <?php if (isset($user['mobile_number']) && !empty($user['mobile_number'])): ?>
                                    <dt><i class="fas fa-phone me-2"></i> Phone</dt>
                                    <dd><?php echo htmlspecialchars($user['mobile_number']); ?></dd>
                                    <?php endif; ?>
                                </dl>
                            </div>

                            <div class="profile-section">
                                <?php if ($user_type === 'supervisor'): ?>
                                <!-- Supervisor Details -->
                                <h5 class="profile-section-title">Supervisor Details</h5>
                                <dl class="profile-details">
                                    <dt><i class="fas fa-building me-2"></i> Department</dt>
                                    <dd><?php echo htmlspecialchars($user['department'] ?? ''); ?></dd>

                                    <!-- Only show Created At if it exists -->
                                    <?php if (isset($user['created_at']) && !empty($user['created_at'])): ?>
                                    <dt><i class="fas fa-calendar-alt me-2"></i> Created At</dt>
                                    <dd><?php echo date('F j, Y', strtotime($user['created_at'])); ?></dd>
                                    <?php endif; ?>
                                </dl>
                                <?php else: ?>
                                <!-- Employee Details -->
                                <h5 class="profile-section-title">Employee Details</h5>
                                <dl class="profile-details">
                                    <dt><i class="fas fa-id-card me-2"></i> Employee ID</dt>
                                    <dd><?php echo htmlspecialchars($user['employee_id'] ?? 'N/A'); ?></dd>

                                    <dt><i class="fas fa-venus-mars me-2"></i> Gender</dt>
                                    <dd><?php echo htmlspecialchars($user['gender'] ?? 'N/A'); ?></dd>

                                    <dt><i class="fas fa-calendar-alt me-2"></i> Date Registered</dt>
                                    <dd><?php echo $date_registered; ?></dd>

                                    <dt><i class="fas fa-calendar-check me-2"></i> Date Started</dt>
                                    <dd><?php echo $date_started; ?></dd>

                                    <?php if (isset($user['employee_type'])): ?>
                                        <dt><i class="fas fa-user-tie me-2"></i> Employee Type</dt>
                                        <dd><?php echo htmlspecialchars($user['employee_type']); ?></dd>
                                    <?php endif; ?>

                                    <?php if (isset($user['no_hours_required']) && $user['no_hours_required'] > 0): ?>
                                        <dt><i class="fas fa-clock me-2"></i> Hours Required</dt>
                                        <dd><?php echo htmlspecialchars($user['no_hours_required']); ?> hours</dd>
                                    <?php endif; ?>
                                </dl>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8">
                    <div class="profile-card">
                        <div class="profile-tabs d-flex">
                            <button type="button" class="profile-tab active" id="edit-profile-tab" onclick="showTab('edit-profile', true)">Edit Profile</button>
                            <button type="button" class="profile-tab" id="change-password-tab" onclick="showTab('change-password', true)">Change Password</button>
                        </div>

                        <div class="profile-content">
                            <form id="profile-form" method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" enctype="multipart/form-data" <?php echo !$user ? 'class="opacity-50"' : ''; ?>>
                                <!-- Hidden file input for profile image -->
                                <input type="file" id="profile_image_upload" name="profile_image" accept="image/*" style="display: none;">

                                <!-- Edit Profile Tab -->
                                <div class="tab-content" id="edit-profile-content">
                                    <?php if ($user_type === 'supervisor'): ?>
                                    <!-- Supervisor Name Fields -->
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div data-mdb-input-init class="form-outline">
                                                <input type="text" id="name" name="name" class="form-control active" value="<?php echo htmlspecialchars($user['name'] ?? ''); ?>" placeholder=" " />
                                                <label class="form-label active" for="name">Full Name</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <div class="mb-4">
                                                <select data-mdb-select-init class="form-select" id="department" name="department">
                                                    <option value="">Select Department</option>
                                                    <option value="Cybersecurity Bureau" <?php echo (isset($user['department']) && $user['department'] === 'Cybersecurity Bureau') ? 'selected' : ''; ?>>Cybersecurity Bureau</option>
                                                    <option value="ILCDB - ICT Literacy and Competency Development Bureau" <?php echo (isset($user['department']) && $user['department'] === 'ILCDB - ICT Literacy and Competency Development Bureau') ? 'selected' : ''; ?>>ILCDB - ICT Literacy and Competency Development Bureau</option>
                                                    <option value="FreeWifi4All" <?php echo (isset($user['department']) && $user['department'] === 'FreeWifi4All') ? 'selected' : ''; ?>>FreeWifi4All</option>
                                                    <option value="eLGU - Electronic Local Government Unit" <?php echo (isset($user['department']) && $user['department'] === 'eLGU - Electronic Local Government Unit') ? 'selected' : ''; ?>>eLGU - Electronic Local Government Unit</option>
                                                </select>
                                                <label class="form-label visually-hidden">Department</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div data-mdb-input-init class="form-outline">
                                                <input type="email" id="gmail" name="gmail" class="form-control active" value="<?php echo htmlspecialchars($user['gmail'] ?? ''); ?>" placeholder=" " />
                                                <label class="form-label active" for="gmail">Email</label>
                                            </div>
                                        </div>
                                    </div>
                                    <?php else: ?>
                                    <!-- Employee Name Fields -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div data-mdb-input-init class="form-outline">
                                                <input type="text" id="first_name" name="first_name" class="form-control" value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" readonly />
                                                <label class="form-label" for="first_name">First Name</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div data-mdb-input-init class="form-outline">
                                                <input type="text" id="surname" name="surname" class="form-control" value="<?php echo htmlspecialchars($user['surname'] ?? ''); ?>" readonly />
                                                <label class="form-label" for="surname">Surname</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div data-mdb-input-init class="form-outline">
                                                <input type="text" id="middle_initial" name="middle_initial" class="form-control" value="<?php echo htmlspecialchars($user['middle_initial'] ?? ''); ?>" readonly />
                                                <label class="form-label" for="middle_initial">Middle Initial</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div data-mdb-input-init class="form-outline">
                                                <input type="email" id="gmail" name="gmail" class="form-control" value="<?php echo htmlspecialchars(blurEmail($user['gmail'] ?? '')); ?>" readonly />
                                                <label class="form-label" for="gmail">Email</label>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="row">
                                        <?php if ($user_type === 'employee'): ?>
                                        <div class="col-md-6">
                                            <div data-mdb-input-init class="form-outline mobile-number-container">
                                                <input type="tel" id="mobile_number_input" class="form-control" maxlength="10" value="<?php echo htmlspecialchars(substr($user['mobile_number'] ?? '+63', 3)); ?>" />
                                                <input type="hidden" id="mobile_number" name="mobile_number" value="<?php echo htmlspecialchars($user['mobile_number'] ?? '+63'); ?>" />
                                                <label class="form-label" for="mobile_number_input">Mobile Number</label>
                                                <span class="mobile-prefix">+63</span>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <?php if ($user_type === 'employee'): ?>
                                        <div class="col-md-6">
                                            <select data-mdb-select-init class="form-select" id="gender" name="gender">
                                                <option value="">Select Gender</option>
                                                <option value="Male" <?php echo (isset($user['gender']) && $user['gender'] === 'Male') ? 'selected' : ''; ?>>Male</option>
                                                <option value="Female" <?php echo (isset($user['gender']) && $user['gender'] === 'Female') ? 'selected' : ''; ?>>Female</option>
                                                <option value="Other" <?php echo (isset($user['gender']) && $user['gender'] === 'Other') ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="mt-4">
                                        <button type="button" id="save_profile_btn" class="btn btn-primary" <?php echo !$user ? 'disabled' : ''; ?>>Save Changes</button>
                                    </div>
                                </div>

                                <!-- Change Password Tab -->
                                <div class="tab-content" id="change-password-content" style="display: none;">
                                    <div class="form-outline mb-4 position-relative" data-mdb-input-init>
                                        <input type="password" id="current_password" name="current_password" class="form-control" placeholder=" " autocomplete="current-password" />
                                        <label class="form-label" for="current_password">Current Password</label>
                                        <span class="password-toggle" data-target="current_password">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>

                                    <div class="form-outline mb-4 position-relative" data-mdb-input-init>
                                        <input type="password" id="new_password" name="new_password" class="form-control" placeholder=" " autocomplete="new-password" />
                                        <label class="form-label" for="new_password">New Password</label>
                                        <span class="password-toggle" data-target="new_password">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>

                                    <div class="form-outline mb-4 position-relative" data-mdb-input-init>
                                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" placeholder=" " autocomplete="new-password" />
                                        <label class="form-label" for="confirm_password">Confirm New Password</label>
                                        <span class="password-toggle" data-target="confirm_password">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>

                                    <div class="mt-4">
                                        <button type="button" id="change_password_btn" class="btn btn-primary" <?php echo !$user ? 'disabled' : ''; ?>>Change Password</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="modern-footer">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="footer-logo-img">
                </div>
                <div class="footer-info">
                    <p class="copyright">&copy; <?php echo date('Y'); ?> OJT Monitoring System</p>
                    <p class="developer">Developed by: <a href="#" class="developer-link">John Lloyd Caban</a></p>
                </div>
            </div>
        </footer>

    </main>

    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>

    <script>
        // Improved tab switching function - defined in global scope
        function showTab(tabName, updateHistory = true) {
            // Get all tab buttons and contents
            const tabs = document.querySelectorAll('.profile-tab');
            const contents = document.querySelectorAll('.tab-content');

            // Remove active class from all tabs
            tabs.forEach(tab => tab.classList.remove('active'));

            // Hide all tab contents
            contents.forEach(content => content.style.display = 'none');

            // Add active class to selected tab
            if (tabName === 'edit-profile') {
                document.getElementById('edit-profile-tab').classList.add('active');
                document.getElementById('edit-profile-content').style.display = 'block';

                // Update URL without reloading, only if updateHistory is true
                if (updateHistory && history.pushState) {
                    history.replaceState({tab: 'edit-profile'}, null, window.location.pathname);
                }
            } else if (tabName === 'change-password') {
                document.getElementById('change-password-tab').classList.add('active');
                document.getElementById('change-password-content').style.display = 'block';

                // Update URL without reloading, only if updateHistory is true
                if (updateHistory && history.pushState) {
                    history.replaceState({tab: 'change-password'}, null, window.location.pathname + '?change-password');
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Check if SweetAlert is loaded
            console.log('SweetAlert2 loaded:', typeof Swal !== 'undefined');

            // Define a safe SweetAlert function that falls back to standard dialogs if SweetAlert is not available
            window.showSweetAlert = function(options) {
                if (typeof Swal !== 'undefined') {
                    return Swal.fire(options);
                } else {
                    // Fallback to standard dialogs
                    if (options.icon === 'error' || options.icon === 'warning') {
                        alert(options.title + '\n\n' + options.text);
                        return { isConfirmed: false };
                    } else if (options.showCancelButton) {
                        const confirmed = confirm(options.title + '\n\n' + options.text);
                        return { isConfirmed: confirmed };
                    } else {
                        alert(options.title + '\n\n' + options.text);
                        return { isConfirmed: true };
                    }
                }
            };

            // Initialize MDB components
            const elements = document.querySelectorAll('[data-mdb-input-init]');
            elements.forEach(element => {
                try {
                    if (mdb && mdb.Input) {
                        const input = new mdb.Input(element);
                        input.init();
                    }

                    // Force activation of labels for inputs with values
                    const inputElement = element.querySelector('input, textarea');
                    if (inputElement && inputElement.value) {
                        inputElement.classList.add('active');
                        const label = element.querySelector('.form-label');
                        if (label) {
                            label.classList.add('active');
                        }
                    }
                } catch (error) {
                    console.warn('Could not initialize input component:', error);
                }
            });

            // Initialize select components
            const selects = document.querySelectorAll('[data-mdb-select-init]');
            selects.forEach(select => {
                try {
                    if (mdb.Select) {
                        new mdb.Select(select).init();
                    } else if (mdb.Dropdown) {
                        // Fallback to Dropdown if Select is not available
                        new mdb.Dropdown(select).init();
                    }
                } catch (error) {
                    console.warn('Could not initialize select component:', error);
                }
            });

            // Handle mobile number input with fixed +63 prefix
            const mobileInput = document.getElementById('mobile_number_input');
            const mobileHidden = document.getElementById('mobile_number');

            if (mobileInput && mobileHidden) {
                // Only allow numbers
                mobileInput.addEventListener('input', function(e) {
                    // Remove any non-numeric characters
                    this.value = this.value.replace(/[^0-9]/g, '');

                    // Update the hidden field with the complete number
                    mobileHidden.value = '+63' + this.value;
                });

                // Prevent deletion of the prefix
                mobileInput.addEventListener('keydown', function(e) {
                    // Allow: backspace, delete, tab, escape, enter
                    if (e.key === 'Backspace' || e.key === 'Delete') {
                        if (this.value.length === 0) {
                            e.preventDefault();
                        }
                    }
                });
            }

            // Check for PHP messages and display them with SweetAlert
            <?php if (!empty($success_message)): ?>
                showSweetAlert({
                    icon: 'success',
                    title: 'Success',
                    text: '<?php echo addslashes($success_message); ?>',
                    confirmButtonColor: '#3b71ca'
                });
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                showSweetAlert({
                    icon: 'error',
                    title: 'Error',
                    text: '<?php echo addslashes($error_message); ?>',
                    confirmButtonColor: '#3b71ca'
                });
            <?php endif; ?>

            // Save Profile button click handler
            document.getElementById('save_profile_btn').addEventListener('click', function() {
                try {
                    console.log('Save Profile button clicked');

                    // Get form data for validation
                    const formData = new FormData(document.getElementById('profile-form'));

                    // Show SweetAlert confirmation
                    showSweetAlert({
                        title: 'Update Profile',
                        text: 'Are you sure you want to update your profile information?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#3b71ca',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Yes, update it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            console.log('Confirmation accepted, submitting form');
                            // Add a hidden input to identify which action to take
                            const hiddenInput = document.createElement('input');
                            hiddenInput.type = 'hidden';
                            hiddenInput.name = 'update_profile';
                            hiddenInput.value = '1';
                            document.getElementById('profile-form').appendChild(hiddenInput);

                            // Add a hidden input to identify which tab to return to
                            const tabInput = document.createElement('input');
                            tabInput.type = 'hidden';
                            tabInput.name = 'active_tab';
                            tabInput.value = 'edit-profile';
                            document.getElementById('profile-form').appendChild(tabInput);

                            // Submit the form
                            document.getElementById('profile-form').submit();
                        }
                    });
                } catch (error) {
                    console.error('Error showing SweetAlert:', error);
                    // Fallback to confirm dialog if SweetAlert fails
                    if (confirm('Are you sure you want to update your profile information?')) {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'update_profile';
                        hiddenInput.value = '1';
                        document.getElementById('profile-form').appendChild(hiddenInput);

                        const tabInput = document.createElement('input');
                        tabInput.type = 'hidden';
                        tabInput.name = 'active_tab';
                        tabInput.value = 'edit-profile';
                        document.getElementById('profile-form').appendChild(tabInput);

                        document.getElementById('profile-form').submit();
                    }
                }
            });

            // Change Password button click handler
            document.getElementById('change_password_btn').addEventListener('click', function() {
                try {
                    console.log('Change Password button clicked');
                    // Validate password fields
                    const currentPassword = document.getElementById('current_password').value;
                    const newPassword = document.getElementById('new_password').value;
                    const confirmPassword = document.getElementById('confirm_password').value;

                    // Validate inputs with SweetAlert
                    if (!currentPassword) {
                        showSweetAlert({
                            icon: 'error',
                            title: 'Error',
                            text: 'Please enter your current password',
                            confirmButtonColor: '#3b71ca'
                        });
                        return;
                    }

                    if (!newPassword) {
                        showSweetAlert({
                            icon: 'error',
                            title: 'Error',
                            text: 'Please enter a new password',
                            confirmButtonColor: '#3b71ca'
                        });
                        return;
                    }

                    if (newPassword.length < 8) {
                        showSweetAlert({
                            icon: 'error',
                            title: 'Error',
                            text: 'New password must be at least 8 characters long',
                            confirmButtonColor: '#3b71ca'
                        });
                        return;
                    }

                    if (newPassword !== confirmPassword) {
                        showSweetAlert({
                            icon: 'error',
                            title: 'Error',
                            text: 'New passwords do not match',
                            confirmButtonColor: '#3b71ca'
                        });
                        return;
                    }

                    // Show confirmation dialog with SweetAlert
                    showSweetAlert({
                        title: 'Change Password',
                        text: 'Are you sure you want to change your password?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#3b71ca',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Yes, change it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            console.log('Password change confirmed, submitting form');
                            // Add a hidden input to identify which action to take
                            const hiddenInput = document.createElement('input');
                            hiddenInput.type = 'hidden';
                            hiddenInput.name = 'update_profile';
                            hiddenInput.value = '1';
                            document.getElementById('profile-form').appendChild(hiddenInput);

                            // Add a hidden input to identify which tab to return to
                            const tabInput = document.createElement('input');
                            tabInput.type = 'hidden';
                            tabInput.name = 'active_tab';
                            tabInput.value = 'change-password';
                            document.getElementById('profile-form').appendChild(tabInput);

                            // Submit the form
                            document.getElementById('profile-form').submit();
                        }
                    });
                } catch (error) {
                    console.error('Error in change password handler:', error);
                    // Fallback to basic validation and confirmation
                    const currentPassword = document.getElementById('current_password').value;
                    const newPassword = document.getElementById('new_password').value;
                    const confirmPassword = document.getElementById('confirm_password').value;

                    if (!currentPassword || !newPassword || newPassword.length < 8 || newPassword !== confirmPassword) {
                        alert('Please check your password inputs');
                        return;
                    }

                    if (confirm('Are you sure you want to change your password?')) {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'update_profile';
                        hiddenInput.value = '1';
                        document.getElementById('profile-form').appendChild(hiddenInput);

                        const tabInput = document.createElement('input');
                        tabInput.type = 'hidden';
                        tabInput.name = 'active_tab';
                        tabInput.value = 'change-password';
                        document.getElementById('profile-form').appendChild(tabInput);

                        document.getElementById('profile-form').submit();
                    }
                }
            });

            // showTab function is now defined in global scope

            // Check URL or PHP variable for initial tab selection
            const currentUrl = window.location.href.toLowerCase();
            const phpActiveTab = '<?php echo $active_tab; ?>';

            if (phpActiveTab === 'change-password' ||
                currentUrl.includes('change-password') ||
                window.location.hash === '#change-password') {
                showTab('change-password', false); // Don't update history on initial load
            } else {
                showTab('edit-profile', false); // Don't update history on initial load
            }

            // Handle browser back/forward buttons
            window.addEventListener('popstate', function(event) {
                if (event.state && event.state.tab) {
                    // Show the tab stored in the history state without updating history again
                    showTab(event.state.tab, false);
                } else {
                    // Default to edit-profile if no state is available
                    showTab('edit-profile', false);
                }
            });

            // Initialize history state for the current tab
            const initialTab = (phpActiveTab === 'change-password' ||
                               currentUrl.includes('change-password') ||
                               window.location.hash === '#change-password') ? 'change-password' : 'edit-profile';

            history.replaceState({tab: initialTab}, null,
                initialTab === 'change-password' ? window.location.pathname + '?change-password' : window.location.pathname);

            // Profile image upload
            const profileImageUpload = document.getElementById('profile_image_upload');
            const profileImageEdit = document.querySelector('.profile-image-edit');

            if (profileImageEdit && profileImageUpload) {
                profileImageEdit.addEventListener('click', function() {
                    profileImageUpload.click();
                });

                profileImageUpload.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const file = this.files[0];

                        // Check file size (max 2MB)
                        if (file.size > 2 * 1024 * 1024) {
                            showSweetAlert({
                                icon: 'error',
                                title: 'File Too Large',
                                text: 'Please select an image less than 2MB in size.',
                                confirmButtonText: 'OK'
                            });
                            this.value = '';
                            return;
                        }

                        // Check file type
                        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
                        if (!validTypes.includes(file.type)) {
                            showSweetAlert({
                                icon: 'error',
                                title: 'Invalid File Type',
                                text: 'Please select a JPG, PNG, or GIF image.',
                                confirmButtonText: 'OK'
                            });
                            this.value = '';
                            return;
                        }

                        // Preview the image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            document.querySelector('.profile-image').src = e.target.result;
                        };
                        reader.readAsDataURL(file);

                        // Show confirmation message
                        showSweetAlert({
                            icon: 'info',
                            title: 'Image Selected',
                            text: 'Click "Save Changes" to update your profile picture.',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }

            // Password toggle functionality
            const passwordToggles = document.querySelectorAll('.password-toggle');

            passwordToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const passwordInput = document.getElementById(targetId);
                    const icon = this.querySelector('i');

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });
        });
    </script>
</body>

</html>
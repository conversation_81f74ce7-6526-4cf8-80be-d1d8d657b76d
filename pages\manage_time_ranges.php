<?php
// manage_time_ranges.php

try {
    $connect = new PDO("mysql:host=localhost;dbname=attendance_monitoring_db", "root", "");
    $connect->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Handle actions from the JavaScript files
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    // Handle different actions
    switch ($action) {
        case 'delete':
            try {
                $id = $_POST['id'];

                // Begin transaction
                $connect->beginTransaction();

                // Check if this is the only time range left
                $countStmt = $connect->prepare("SELECT COUNT(*) FROM tbl_time_ranges");
                $countStmt->execute();
                $totalCount = $countStmt->fetchColumn();

                if ($totalCount <= 1) {
                    // Rollback the transaction
                    $connect->rollBack();

                    // Return error JSON response
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Cannot delete the last time range. At least one time range must remain in the system.'
                    ]);
                    exit;
                }

                // Check if this is the only active time range
                $activeStmt = $connect->prepare("SELECT activate_this_timerange FROM tbl_time_ranges WHERE id = ?");
                $activeStmt->execute([$id]);
                $isActive = $activeStmt->fetchColumn();

                if ($isActive == 1) {
                    // Check if this is the only active time range
                    $activeCountStmt = $connect->prepare("SELECT COUNT(*) FROM tbl_time_ranges WHERE activate_this_timerange = 1");
                    $activeCountStmt->execute();
                    $activeCount = $activeCountStmt->fetchColumn();

                    if ($activeCount <= 1) {
                        // Rollback the transaction
                        $connect->rollBack();

                        // Return error JSON response
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Cannot delete the only active time range. Please activate another time range before deleting this one.'
                        ]);
                        exit;
                    }
                }

                // Delete from database
                $stmt = $connect->prepare("DELETE FROM tbl_time_ranges WHERE id = ?");
                $stmt->execute([$id]);

                // Commit the transaction
                $connect->commit();

                // Return JSON response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Time range deleted successfully!'
                ]);
                exit;
            } catch (Exception $e) {
                // Return error JSON response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Error deleting time range: ' . $e->getMessage()
                ]);
                exit;
            }

        case 'toggle_status':
            try {
                $id = $_POST['id'];
                $currentStatus = $_POST['current_status'];

                // Toggle the status (if 1 then 0, if 0 then 1)
                $newStatus = $currentStatus == 1 ? 0 : 1;

                // Begin transaction to ensure data consistency
                $connect->beginTransaction();

                // If trying to deactivate (current status is 1, new status is 0)
                if ($currentStatus == 1 && $newStatus == 0) {
                    // Check if this is the only active time range
                    $countStmt = $connect->prepare("SELECT COUNT(*) FROM tbl_time_ranges WHERE activate_this_timerange = 1");
                    $countStmt->execute();
                    $activeCount = $countStmt->fetchColumn();

                    // If there's only one active time range, prevent deactivation
                    if ($activeCount <= 1) {
                        // Rollback the transaction
                        $connect->rollBack();

                        // Return error JSON response
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Cannot deactivate the last active time range. At least one time range must remain active.'
                        ]);
                        exit;
                    }
                }

                if ($newStatus == 1) {
                    // If activating this time range, deactivate all other time ranges first
                    $stmt = $connect->prepare("UPDATE tbl_time_ranges SET activate_this_timerange = 0 WHERE id != ?");
                    $stmt->execute([$id]);
                }

                // Update the status of the selected time range
                $stmt = $connect->prepare("UPDATE tbl_time_ranges SET activate_this_timerange = ? WHERE id = ?");
                $stmt->execute([$newStatus, $id]);

                // Commit the transaction
                $connect->commit();

                // Return JSON response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => $newStatus == 1 ? 'Time range activated successfully!' : 'Time range deactivated successfully!',
                    'new_status' => $newStatus
                ]);
                exit;
            } catch (Exception $e) {
                // Rollback the transaction in case of error
                if ($connect->inTransaction()) {
                    $connect->rollBack();
                }

                // Return error JSON response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Error updating status: ' . $e->getMessage()
                ]);
                exit;
            }

        case 'update':
            try {
                // Get form data
                $id = $_POST['id'];
                $currentDate = date('Y-m-d');

                // Validate that all required time fields are present
                $requiredFields = [
                    'morning_time_in_start',
                    'morning_time_in_end',
                    'morning_time_out_start',
                    'morning_time_out_end',
                    'afternoon_time_in_start',
                    'afternoon_time_in_end',
                    'afternoon_time_out_start',
                    'afternoon_time_out_end'
                ];

                foreach ($requiredFields as $field) {
                    if (!isset($_POST[$field]) || empty($_POST[$field])) {
                        throw new Exception("Missing required field: $field");
                    }
                }

                // Convert time inputs to datetime format for database
                $morning_time_in_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_in_start']}"));
                $morning_time_in_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_in_end']}"));
                $morning_time_out_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_out_start']}"));
                $morning_time_out_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_out_end']}"));
                $afternoon_time_in_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_in_start']}"));
                $afternoon_time_in_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_in_end']}"));
                $afternoon_time_out_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_out_start']}"));
                $afternoon_time_out_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_out_end']}"));

                // Get current activation status from database
                $stmt = $connect->prepare("SELECT activate_this_timerange FROM tbl_time_ranges WHERE id = ?");
                $stmt->execute([$id]);
                $currentTimeRange = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$currentTimeRange) {
                    throw new Exception("Time range not found");
                }

                $activate_this_timerange = $currentTimeRange['activate_this_timerange'];

                // Update database
                $stmt = $connect->prepare("UPDATE tbl_time_ranges SET
                    morning_time_in_start = ?, morning_time_in_end = ?,
                    morning_time_out_start = ?, morning_time_out_end = ?,
                    afternoon_time_in_start = ?, afternoon_time_in_end = ?,
                    afternoon_time_out_start = ?, afternoon_time_out_end = ?,
                    activate_this_timerange = ?
                    WHERE id = ?");

                $stmt->execute([
                    $morning_time_in_start,
                    $morning_time_in_end,
                    $morning_time_out_start,
                    $morning_time_out_end,
                    $afternoon_time_in_start,
                    $afternoon_time_in_end,
                    $afternoon_time_out_start,
                    $afternoon_time_out_end,
                    $activate_this_timerange,
                    $id
                ]);

                // Return JSON response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Time range updated successfully!'
                ]);
                exit;
            } catch (Exception $e) {
                // Return error JSON response
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Error updating time range: ' . $e->getMessage()
                ]);
                exit;
            }

        default:
            // Invalid action
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action: ' . $action
            ]);
            exit;
    }
}

// Handle toggle status request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_status'])) {
    try {
        $id = $_POST['id'];
        $currentStatus = $_POST['current_status'];

        // Toggle the status (if 1 then 0, if 0 then 1)
        $newStatus = $currentStatus == 1 ? 0 : 1;

        // Begin transaction to ensure data consistency
        $connect->beginTransaction();

        if ($newStatus == 1) {
            // If activating this time range, deactivate all other time ranges first
            $stmt = $connect->prepare("UPDATE tbl_time_ranges SET activate_this_timerange = 0 WHERE id != ?");
            $stmt->execute([$id]);
        }

        // Update the status of the selected time range
        $stmt = $connect->prepare("UPDATE tbl_time_ranges SET activate_this_timerange = ? WHERE id = ?");
        $stmt->execute([$newStatus, $id]);

        // Commit the transaction
        $connect->commit();

        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => $newStatus == 1 ? 'Time range activated successfully!' : 'Time range deactivated successfully!',
            'new_status' => $newStatus
        ]);
        exit;
    } catch (Exception $e) {
        // Rollback the transaction in case of error
        if ($connect->inTransaction()) {
            $connect->rollBack();
        }

        // Return error JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error updating status: ' . $e->getMessage()
        ]);
        exit;
    }
}

// Handle delete time range request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_time_range'])) {
    try {
        $id = $_POST['id'];

        // Delete from database
        $stmt = $connect->prepare("DELETE FROM tbl_time_ranges WHERE id = ?");
        $stmt->execute([$id]);

        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Time range deleted successfully!'
        ]);
        exit;
    } catch (Exception $e) {
        // Return error JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error deleting time range: ' . $e->getMessage()
        ]);
        exit;
    }
}

// Handle get time range data for editing
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['get_time_range'])) {
    try {
        $id = $_GET['id'];

        // Get time range data
        $stmt = $connect->prepare("SELECT * FROM tbl_time_ranges WHERE id = ?");
        $stmt->execute([$id]);
        $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($timeRange) {
            // Format times for display in form (12-hour format with AM/PM)
            $timeRange['morning_time_in_start'] = date('h:i A', strtotime($timeRange['morning_time_in_start']));
            $timeRange['morning_time_in_end'] = date('h:i A', strtotime($timeRange['morning_time_in_end']));
            $timeRange['morning_time_out_start'] = date('h:i A', strtotime($timeRange['morning_time_out_start']));
            $timeRange['morning_time_out_end'] = date('h:i A', strtotime($timeRange['morning_time_out_end']));
            $timeRange['afternoon_time_in_start'] = date('h:i A', strtotime($timeRange['afternoon_time_in_start']));
            $timeRange['afternoon_time_in_end'] = date('h:i A', strtotime($timeRange['afternoon_time_in_end']));
            $timeRange['afternoon_time_out_start'] = date('h:i A', strtotime($timeRange['afternoon_time_out_start']));
            $timeRange['afternoon_time_out_end'] = date('h:i A', strtotime($timeRange['afternoon_time_out_end']));

            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $timeRange
            ]);
        } else {
            // Return error JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Time range not found'
            ]);
        }
        exit;
    } catch (Exception $e) {
        // Return error JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error getting time range data: ' . $e->getMessage()
        ]);
        exit;
    }
}

// Handle update time range request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_time_range'])) {
    try {
        // Get form data
        $id = $_POST['id'];
        $currentDate = date('Y-m-d');

        // Validate that all required time fields are present
        $requiredFields = [
            'morning_time_in_start',
            'morning_time_in_end',
            'morning_time_out_start',
            'morning_time_out_end',
            'afternoon_time_in_start',
            'afternoon_time_in_end',
            'afternoon_time_out_start',
            'afternoon_time_out_end'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }

        // Convert time inputs to datetime format for database
        $morning_time_in_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_in_start']}"));
        $morning_time_in_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_in_end']}"));
        $morning_time_out_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_out_start']}"));
        $morning_time_out_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_out_end']}"));
        $afternoon_time_in_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_in_start']}"));
        $afternoon_time_in_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_in_end']}"));
        $afternoon_time_out_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_out_start']}"));
        $afternoon_time_out_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_out_end']}"));

        // Get current activation status from database
        $stmt = $connect->prepare("SELECT activate_this_timerange FROM tbl_time_ranges WHERE id = ?");
        $stmt->execute([$id]);
        $currentTimeRange = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$currentTimeRange) {
            throw new Exception("Time range not found");
        }

        $activate_this_timerange = $currentTimeRange['activate_this_timerange'];

        // Update database
        $stmt = $connect->prepare("UPDATE tbl_time_ranges SET
            morning_time_in_start = ?, morning_time_in_end = ?,
            morning_time_out_start = ?, morning_time_out_end = ?,
            afternoon_time_in_start = ?, afternoon_time_in_end = ?,
            afternoon_time_out_start = ?, afternoon_time_out_end = ?,
            activate_this_timerange = ?
            WHERE id = ?");

        $stmt->execute([
            $morning_time_in_start,
            $morning_time_in_end,
            $morning_time_out_start,
            $morning_time_out_end,
            $afternoon_time_in_start,
            $afternoon_time_in_end,
            $afternoon_time_out_start,
            $afternoon_time_out_end,
            $activate_this_timerange,
            $id
        ]);

        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Time range updated successfully!'
        ]);
        exit;
    } catch (Exception $e) {
        // Return error JSON response
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Error updating time range: ' . $e->getMessage()
        ]);
        exit;
    }
}

// Handle form submission for adding a new time range
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_time_range'])) {
    try {
        // Get form data
        $currentDate = date('Y-m-d');

        // Validate that all required time fields are present
        $requiredFields = [
            'morning_time_in_start',
            'morning_time_in_end',
            'morning_time_out_start',
            'morning_time_out_end',
            'afternoon_time_in_start',
            'afternoon_time_in_end',
            'afternoon_time_out_start',
            'afternoon_time_out_end'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($_POST[$field]) || empty($_POST[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }

        // Convert time inputs to datetime format for database
        $morning_time_in_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_in_start']}"));
        $morning_time_in_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_in_end']}"));
        $morning_time_out_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_out_start']}"));
        $morning_time_out_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['morning_time_out_end']}"));
        $afternoon_time_in_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_in_start']}"));
        $afternoon_time_in_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_in_end']}"));
        $afternoon_time_out_start = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_out_start']}"));
        $afternoon_time_out_end = date('Y-m-d H:i:s', strtotime("$currentDate {$_POST['afternoon_time_out_end']}"));

        // New time ranges are inactive by default
        // Activation is handled separately through the toggle button in the Actions column
        $activate_this_timerange = 0;

        // Begin transaction to ensure data consistency
        $connect->beginTransaction();

        // No need to check for activation since new time ranges are always inactive by default

        // Insert into database
        $stmt = $connect->prepare("INSERT INTO tbl_time_ranges
            (morning_time_in_start, morning_time_in_end, morning_time_out_start, morning_time_out_end,
            afternoon_time_in_start, afternoon_time_in_end, afternoon_time_out_start, afternoon_time_out_end,
            activate_this_timerange)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");

        $stmt->execute([
            $morning_time_in_start,
            $morning_time_in_end,
            $morning_time_out_start,
            $morning_time_out_end,
            $afternoon_time_in_start,
            $afternoon_time_in_end,
            $afternoon_time_out_start,
            $afternoon_time_out_end,
            $activate_this_timerange
        ]);

        // Commit the transaction
        $connect->commit();

        // Success message will be handled by SweetAlert

        // Return JSON response for AJAX requests
        // Check for the X-Requested-With header or a specific POST parameter for AJAX detection
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Time range added successfully!']);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_time_ranges.php");
        exit;
    } catch (PDOException $e) {
        // Rollback the transaction in case of error
        if ($connect->inTransaction()) {
            $connect->rollBack();
        }

        // Error message will be handled by SweetAlert
        $errorMessage = "Database error: " . $e->getMessage();

        // Return JSON response for AJAX requests
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_time_ranges.php");
        exit;
    } catch (Exception $e) {
        // Rollback the transaction in case of error
        if ($connect->inTransaction()) {
            $connect->rollBack();
        }

        // Error message will be handled by SweetAlert
        $errorMessage = "Error adding time range: " . $e->getMessage();

        // Return JSON response for AJAX requests
        if ((isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') || isset($_POST['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit;
        }

        // Redirect for non-AJAX requests
        header("Location: manage_time_ranges.php");
        exit;
    }
}

// Pagination setup - completely rewritten to ensure all variables are integers
$recordsPerPageOptions = array(5, 10, 25, 50, 100, 250, 500);
$defaultRecordsPerPage = 5;

// Get recordsPerPage from GET parameter, default to 5 if not set or invalid
$recordsPerPage = $defaultRecordsPerPage;
if (isset($_GET['recordsPerPage'])) {
    $requestedRecordsPerPage = intval($_GET['recordsPerPage']);
    if (in_array($requestedRecordsPerPage, $recordsPerPageOptions)) {
        $recordsPerPage = $requestedRecordsPerPage;
    }
}

// Get current page from GET parameter, default to 1 if not set or invalid
$currentPage = 1;
if (isset($_GET['page'])) {
    $requestedPage = intval($_GET['page']);
    if ($requestedPage > 0) {
        $currentPage = $requestedPage;
    }
}

// Calculate the starting record for pagination
$startFrom = ($currentPage - 1) * $recordsPerPage;

// Get total number of time ranges
$totalTimeRangesQuery = $connect->query("SELECT COUNT(*) as total FROM tbl_time_ranges");
$totalTimeRangesRow = $totalTimeRangesQuery->fetch(PDO::FETCH_ASSOC);
$totalTimeRanges = intval($totalTimeRangesRow['total']);

// Calculate total pages
$totalPages = intval(ceil($totalTimeRanges / $recordsPerPage));

// Fetch time ranges with pagination
$stmt = $connect->prepare("SELECT * FROM tbl_time_ranges ORDER BY id DESC LIMIT :start, :recordsPerPage");
$stmt->bindValue(':start', $startFrom, PDO::PARAM_INT);
$stmt->bindValue(':recordsPerPage', $recordsPerPage, PDO::PARAM_INT);
$stmt->execute();
$timeRanges = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5">
    <meta name="description" content="OJT Monitoring System is a platform designed to track and manage on-the-job training programs efficiently.">
    <meta name="author" content="Your Name or Organization">
    <meta name="theme-color" content="#FFFFFF">

    <link rel="icon" href="../assets/img/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="../assets/boxicons-master/css/boxicons.min.css">

    <title>OJT Monitoring System - Manage Time Ranges</title>
    <link rel="stylesheet" href="main_style.css">

    <style>
        /* Modern UI Styles for Rows Dropdown and Pagination */
        .modern-dropdown {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modern-dropdown .dropdown-label {
            font-size: 14px;
            font-weight: 500;
            color: #555;
        }

        .modern-dropdown .btn-dropdown {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 80px;
            transition: all 0.2s ease;
        }

        .modern-dropdown .btn-dropdown:hover {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .modern-dropdown .dropdown-menu {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            padding: 4px 0;
        }

        .modern-dropdown .dropdown-item {
            padding: 6px 16px;
            font-size: 14px;
            color: #333;
            transition: all 0.2s ease;
        }

        .modern-dropdown .dropdown-item:hover {
            background: rgba(0, 123, 255, 0.1);
        }

        /* Table Styles */
        .table-responsive {
            margin-bottom: 0;
        }

        .table {
            margin-bottom: 0;
        }

        .table tfoot {
            margin-bottom: 0;
        }

        /* Modern Pagination */
        .modern-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            height: auto;
            padding: 0 4px;
        }

        .modern-pagination .page-info {
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }

        .modern-pagination .nav-buttons {
            display: flex;
            gap: 8px;
        }

        .modern-pagination .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            color: #333;
            font-size: 14px;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .modern-pagination .nav-btn:hover:not(.disabled) {
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            color: #007bff;
        }

        .modern-pagination .nav-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 425px) {

            .modern-dropdown .btn-dropdown,
            .modern-pagination .nav-btn {
                padding: 4px 8px;
                font-size: 12px;
                height: 32px;
                width: 32px;
            }

            .modern-dropdown .dropdown-label,
            .modern-pagination .page-info {
                font-size: 12px;
            }
        }

        @media (max-width: 375px) {
            .modern-pagination .nav-btn {
                padding: 3px 6px;
                font-size: 11px;
                height: 30px;
                width: 30px;
            }

            .modern-pagination .page-info {
                font-size: 11px;
            }
        }

        @media (max-width: 320px) {
            .modern-dropdown .btn-dropdown {
                min-width: 60px;
                padding: 3px 6px;
                font-size: 11px;
                height: 28px;
            }

            .modern-pagination .nav-btn {
                padding: 2px 5px;
                font-size: 10px;
                height: 28px;
                width: 28px;
            }

            .modern-pagination .page-info {
                font-size: 10px;
            }
        }
    </style>
</head>

<body>
    <?php include 'sidebar.php' ?>

    <div class="main-content" id="main-content">
        <?php include 'navbar_header.php' ?>
        <div class="content" style="border-radius: 20px; margin-left: 10px; margin-right: 10px;">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Manage Time Range</li>
                </ol>
            </nav>
            <main>
                <!-- SweetAlert handles success/error messages -->

                <div class="row">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mt-3 mb-3">
                                <div>
                                    <h2 style="font-size: 16px;">Time Ranges</h2>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="modern-dropdown me-2">
                                        <span class="dropdown-label">Rows:</span>
                                        <div class="btn-group">
                                            <button class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                <?php echo $recordsPerPage; ?>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php foreach ($recordsPerPageOptions as $option): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="?recordsPerPage=<?php echo intval($option); ?>&page=1">
                                                            <?php echo intval($option); ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="me-2" style="width: 200px;">
                                        <input type="search" id="searchTimeRange" class="form-control" placeholder="Search time..." />
                                    </div>
                                    <button type="button" class="btn btn-md btn-primary" style="text-transform: capitalize; font-size: 12px;" data-mdb-modal-init data-mdb-target="#addTimeRangeModal">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="table-responsive card">
                                <table class="table table-hover table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">#</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Morning Time In</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Morning Time Out</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Afternoon Time In</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Afternoon Time Out</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Status</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="timeRangesTableBody">

                                        <?php if (count($timeRanges) > 0): ?>
                                            <?php
                                            // Calculate counter for row numbering
                                            $counter = $startFrom + 1; // startFrom is already calculated correctly above

                                            foreach ($timeRanges as $timeRange):
                                            ?>
                                                <tr>
                                                    <td class="text-center"><?php echo $counter++; ?></td>
                                                    <td class="text-center"><?php echo date('h:i A', strtotime($timeRange['morning_time_in_start'])) . ' - ' . date('h:i A', strtotime($timeRange['morning_time_in_end'])); ?></td>
                                                    <td class="text-center"><?php echo date('h:i A', strtotime($timeRange['morning_time_out_start'])) . ' - ' . date('h:i A', strtotime($timeRange['morning_time_out_end'])); ?></td>
                                                    <td class="text-center"><?php echo date('h:i A', strtotime($timeRange['afternoon_time_in_start'])) . ' - ' . date('h:i A', strtotime($timeRange['afternoon_time_in_end'])); ?></td>
                                                    <td class="text-center"><?php echo date('h:i A', strtotime($timeRange['afternoon_time_out_start'])) . ' - ' . date('h:i A', strtotime($timeRange['afternoon_time_out_end'])); ?></td>
                                                    <td class="text-center">
                                                        <?php if ($timeRange['activate_this_timerange'] == 1): ?>
                                                            <span class="badge bg-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-danger">Inactive</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm <?php echo $timeRange['activate_this_timerange'] == 1 ? 'btn-secondary' : 'btn-success'; ?> btn-toggle-status" data-id="<?php echo $timeRange['id']; ?>" data-status="<?php echo $timeRange['activate_this_timerange']; ?>">
                                                            <i class="fas <?php echo $timeRange['activate_this_timerange'] == 1 ? 'fa-toggle-off' : 'fa-toggle-on'; ?>"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-warning btn-edit" data-id="<?php echo $timeRange['id']; ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger btn-delete" data-id="<?php echo $timeRange['id']; ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>

                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="7" class="text-center">No time ranges found</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">#</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Morning Time In</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Morning Time Out</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Afternoon Time In</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Afternoon Time Out</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Status</th>
                                            <th style="background-color:rgb(228, 228, 228);" scope="col" class="text-center">Actions</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Modern Pagination Controls -->
                            <div class="modern-pagination">
                                <!-- Page Information (Left) -->
                                <div class="page-info">
                                    <?php
                                    // Calculate the range of rows being displayed - ensure all values are integers
                                    $startRow = intval($startFrom) + 1; // Use startFrom which is already calculated correctly
                                    $endRow = min($startRow + intval($recordsPerPage) - 1, intval($totalTimeRanges));

                                    // Handle case when there are no records
                                    if (intval($totalTimeRanges) === 0) {
                                        $startRow = 0;
                                        $endRow = 0;
                                    }
                                    ?>
                                    Result of <?php echo $startRow; ?>-<?php echo $endRow; ?> of <?php echo $totalTimeRanges; ?>
                                </div>

                                <!-- Navigation Buttons (Right) -->
                                <div class="nav-buttons">
                                    <!-- Previous Button -->
                                    <?php $currentPageInt = intval($currentPage); // Ensure currentPage is an integer
                                    ?>
                                    <a class="nav-btn <?php echo $currentPageInt == 1 ? 'disabled' : ''; ?>"
                                        href="<?php echo $currentPageInt > 1 ? '?page=' . ($currentPageInt - 1) . '&recordsPerPage=' . intval($recordsPerPage) : 'javascript:void(0)'; ?>"
                                        aria-label="Previous"
                                        <?php echo $currentPageInt == 1 ? 'onclick="return false;"' : ''; ?>>
                                        <i class="fa fa-chevron-left"></i>
                                    </a>

                                    <!-- Next Button -->
                                    <?php $totalPagesInt = intval($totalPages); // Ensure totalPages is an integer
                                    ?>
                                    <a class="nav-btn <?php echo $currentPageInt == $totalPagesInt ? 'disabled' : ''; ?>"
                                        href="<?php echo $currentPageInt < $totalPagesInt ? '?page=' . ($currentPageInt + 1) . '&recordsPerPage=' . intval($recordsPerPage) : 'javascript:void(0)'; ?>"
                                        aria-label="Next"
                                        <?php echo $currentPageInt == $totalPagesInt ? 'onclick="return false;"' : ''; ?>>
                                        <i class="fa fa-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        <!-- Footer -->
        <footer class="bg-link text-center ">

            <!-- Copyright -->
            <div class="text-center p-2 mt-5" style="background-color: #F5F5F9;">
                © 2025 Developed by:
                <a class="text-dark" href="#">John Lloyd Caban</a>
            </div>
            <!-- Copyright -->

        </footer>
        <!-- Footer -->
    </div>

    <!-- ADD TIME RANGE MODAL -->
    <div class="modal fade" id="addTimeRangeModal" tabindex="-1" aria-labelledby="addTimeRangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="border-radius: 12px; overflow: hidden; box-shadow: 0 15px 35px rgba(0,0,0,0.15);">
                <div class="modal-header bg-primary text-white" style="padding: 16px 24px;">
                    <h5 class="modal-title" id="addTimeRangeModalLabel" style="font-weight: 600;">Add New Time Range</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 24px;">
                    <form id="addTimeRangeForm" method="POST">
                        <input type="hidden" name="add_time_range" value="1">

                        <div class="card mb-4" style="border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                            <div class="card-header bg-light" style="padding: 12px 20px;">
                                <h6 class="mb-0 text-primary"><i class="fas fa-sun me-2"></i>Morning Schedule</h6>
                            </div>
                            <div class="card-body" style="padding: 20px;">
                                <div class="row mb-4">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time In Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="morning_time_in_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="morning_time_in_start" name="morning_time_in_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="morning_time_in_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="morning_time_in_end" name="morning_time_in_end" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time Out Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="morning_time_out_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="morning_time_out_start" name="morning_time_out_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="morning_time_out_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="morning_time_out_end" name="morning_time_out_end" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4" style="border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.05);">
                            <div class="card-header bg-light" style="padding: 12px 20px;">
                                <h6 class="mb-0 text-primary"><i class="fas fa-moon me-2"></i>Afternoon Schedule</h6>
                            </div>
                            <div class="card-body" style="padding: 20px;">
                                <div class="row mb-4">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time In Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="afternoon_time_in_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="afternoon_time_in_start" name="afternoon_time_in_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="afternoon_time_in_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="afternoon_time_in_end" name="afternoon_time_in_end" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time Out Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="afternoon_time_out_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="afternoon_time_out_start" name="afternoon_time_out_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="afternoon_time_out_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="afternoon_time_out_end" name="afternoon_time_out_end" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activation is handled separately through the toggle button in the Actions column -->

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-mdb-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Time Range
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Time Range Modal -->
    <div class="modal fade" id="editTimeRangeModal" tabindex="-1" aria-labelledby="editTimeRangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editTimeRangeModalLabel">Edit Time Range</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editTimeRangeForm" method="post">
                        <input type="hidden" name="update_time_range" value="1">
                        <input type="hidden" name="id" id="edit_id">

                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fas fa-sun me-2"></i> Morning Schedule</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time In Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_morning_time_in_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_morning_time_in_start" name="morning_time_in_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_morning_time_in_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_morning_time_in_end" name="morning_time_in_end" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time Out Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_morning_time_out_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_morning_time_out_start" name="morning_time_out_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_morning_time_out_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_morning_time_out_end" name="morning_time_out_end" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fas fa-moon me-2"></i> Afternoon Schedule</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time In Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_afternoon_time_in_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_afternoon_time_in_start" name="afternoon_time_in_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_afternoon_time_in_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_afternoon_time_in_end" name="afternoon_time_in_end" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <h6 class="text-muted">Time Out Range</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_afternoon_time_out_start" class="form-label">Start Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_afternoon_time_out_start" name="afternoon_time_out_start" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_afternoon_time_out_end" class="form-label">End Time</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                            <input type="time" class="form-control" id="edit_afternoon_time_out_end" name="afternoon_time_out_end" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Toggle status is handled separately in the Actions column -->

                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" data-mdb-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>Update Time Range
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="../assets/node_modules/sweetalert2/dist/sweetalert2.min.js"></script>
    <script src="../assets/js/toggle_status_fix.js" defer></script>
    <script src="../assets/js/edit_form_fix.js" defer></script>
    <script src="../assets/js/delete_button_fix.js" defer></script>
    <script src="../assets/js/search_time_range.js" defer></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // No default values for time inputs - user will input manually

            // Add Time Range Form Submission with AJAX
            const addTimeRangeForm = document.getElementById('addTimeRangeForm');
            if (addTimeRangeForm) {
                addTimeRangeForm.addEventListener('submit', function(event) {
                    event.preventDefault();

                    const formData = new FormData(this);
                    // Add an AJAX parameter to ensure the server recognizes this as an AJAX request
                    formData.append('ajax', '1');

                    fetch('manage_time_ranges.php', {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Response data:', data);
                            // Close the modal
                            const modal = document.getElementById('addTimeRangeModal');

                            // Try to close with Bootstrap 5 first
                            if (typeof bootstrap !== 'undefined') {
                                const bsModal = bootstrap.Modal.getInstance(modal);
                                if (bsModal) {
                                    bsModal.hide();
                                }
                            }
                            // Fallback to MDB if Bootstrap didn't work
                            else if (typeof mdb !== 'undefined') {
                                const mdbModal = mdb.Modal.getInstance(modal);
                                if (mdbModal) {
                                    mdbModal.hide();
                                }
                            }
                            // Manual fallback if neither library is available
                            else {
                                $(modal).modal('hide'); // jQuery fallback
                            }

                            // Show success message
                            Swal.fire({
                                icon: data.success ? 'success' : 'error',
                                title: data.success ? 'Success!' : 'Error!',
                                text: data.message,
                                allowOutsideClick: false,
                                confirmButtonText: 'OK'
                            }).then(() => {
                                // Reload the page to show the new time range
                                location.reload();
                            });
                        })
                        .catch(error => {
                            console.error('Error adding time range:', error);
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Failed to add time range.',
                                allowOutsideClick: false,
                                confirmButtonText: 'OK'
                            });
                        });
                });
            }

            // Handle Edit Button Click
            document.querySelectorAll('.btn-edit').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');

                    // Fetch time range data
                    fetch(`manage_time_ranges.php?get_time_range=1&id=${id}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Populate form fields
                                document.getElementById('edit_id').value = data.data.id;
                                // Convert 12-hour format (e.g., "07:00 AM") to 24-hour format for input fields
                                function convertTo24Hour(timeStr) {
                                    if (!timeStr) return '';

                                    // Extract time and AM/PM
                                    const [time, period] = timeStr.split(' ');
                                    let [hours, minutes] = time.split(':');

                                    // Convert hours to 24-hour format
                                    hours = parseInt(hours);
                                    if (period === 'PM' && hours < 12) hours += 12;
                                    if (period === 'AM' && hours === 12) hours = 0;

                                    // Format as HH:MM
                                    return `${hours.toString().padStart(2, '0')}:${minutes}`;
                                }

                                document.getElementById('edit_morning_time_in_start').value = convertTo24Hour(data.data.morning_time_in_start);
                                document.getElementById('edit_morning_time_in_end').value = convertTo24Hour(data.data.morning_time_in_end);
                                document.getElementById('edit_morning_time_out_start').value = convertTo24Hour(data.data.morning_time_out_start);
                                document.getElementById('edit_morning_time_out_end').value = convertTo24Hour(data.data.morning_time_out_end);
                                document.getElementById('edit_afternoon_time_in_start').value = convertTo24Hour(data.data.afternoon_time_in_start);
                                document.getElementById('edit_afternoon_time_in_end').value = convertTo24Hour(data.data.afternoon_time_in_end);
                                document.getElementById('edit_afternoon_time_out_start').value = convertTo24Hour(data.data.afternoon_time_out_start);
                                document.getElementById('edit_afternoon_time_out_end').value = convertTo24Hour(data.data.afternoon_time_out_end);
                                // Activation status is handled separately in the Actions column

                                // Show modal
                                const modal = document.getElementById('editTimeRangeModal');
                                const bsModal = new bootstrap.Modal(modal);
                                bsModal.show();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: data.message,
                                    allowOutsideClick: false,
                                    confirmButtonText: 'OK'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching time range data:', error);
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Failed to fetch time range data.',
                                allowOutsideClick: false,
                                confirmButtonText: 'OK'
                            });
                        });
                });
            });

        });
    </script>
</body>

</html>